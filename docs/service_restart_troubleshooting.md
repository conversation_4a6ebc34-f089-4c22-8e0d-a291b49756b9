# Alpha Service 重启问题排查指南

## 问题描述

你遇到的问题是：服务没有手动重启，但是日志中出现了"启动后脚本执行失败"的错误信息。

## 问题原因分析

### 1. Circus进程管理器自动重启
- **Circus配置**: `conf/circus.ini` 中设置了 `check_delay=5`
- **自动重启机制**: 每5秒检查一次进程状态，如果检测到异常会自动重启服务
- **重启触发**: 进程崩溃、健康检查失败、内存不足等

### 2. 可能的触发原因
- **异步任务回调异常**: `exception calling callback for <Future at 0x7f226035eb40 state=cancelled>`
- **RocketMQ连接问题**: 日志中显示RocketMQ相关错误
- **系统资源不足**: 内存、CPU、磁盘空间等
- **网络连接问题**: 外部服务连接超时

### 3. 启动后脚本执行
- **执行时机**: 每次服务启动时，`lifespan`函数都会执行启动后脚本
- **脚本内容**: 启动schedulerxAgent等外部服务
- **重复执行**: 服务重启导致脚本重复执行

## 解决方案

### 1. 代码优化（已实施）

#### 启动脚本防重复执行
```python
# 在 src/presentation/api/pythonic_server.py 中
startup_flag_file = "/tmp/alpha_service_startup_completed"
if os.path.exists(startup_flag_file):
    logger.info("启动后脚本已执行过，跳过重复执行")
else:
    # 执行启动脚本
    # 创建标志文件
```

#### 添加超时控制
```python
result = subprocess.run(
    "export JAVA_HOME=/opt/taobao/java && export PATH=$JAVA_HOME/bin:$PATH && java -version &>/dev/null && sh $ADMIN_HOME/schedulerxAgent/bin/start.sh", 
    shell=True, 
    check=True, 
    capture_output=True, 
    text=True,
    timeout=60  # 添加60秒超时
)
```

### 2. 监控和诊断工具

#### 快速诊断工具
```bash
# 运行快速诊断
python tools/quick_diagnosis.py
```

#### 持续监控工具
```bash
# 检查当前状态
python tools/monitor_service_restarts.py status

# 持续监控（每30秒检查一次）
python tools/monitor_service_restarts.py monitor

# 自定义检查间隔（每60秒检查一次）
python tools/monitor_service_restarts.py monitor 60
```

### 3. 手动排查步骤

#### 步骤1: 检查Circus进程
```bash
# 检查Circus进程状态
ps aux | grep circusd

# 检查Circus配置文件
ls -la /home/<USER>/wuying-alpha-service/conf/circus.ini
```

#### 步骤2: 检查Python进程
```bash
# 检查Python服务进程
ps aux | grep "start_service.py"

# 检查进程数量（应该只有1个）
ps aux | grep "start_service.py" | wc -l
```

#### 步骤3: 检查健康状态
```bash
# 检查app.online文件
cat /home/<USER>/app.online

# 检查HTTP健康检查
curl -s http://127.0.0.1:8000/status.taobao
```

#### 步骤4: 检查日志
```bash
# 查看应用日志最后10行
tail -10 /home/<USER>/wuying-alpha-service/logs/application.log

# 查看错误日志最后10行
tail -10 /home/<USER>/wuying-alpha-service/logs/error.log

# 搜索重启相关日志
grep "启动Pythonic Alpha Service" /home/<USER>/wuying-alpha-service/logs/application.log | tail -5
```

#### 步骤5: 检查系统资源
```bash
# 检查内存使用
free -h

# 检查磁盘使用
df -h

# 检查系统负载
uptime
```

### 4. 常见问题解决

#### 问题1: Circus进程未运行
```bash
# 启动Circus
circusd /home/<USER>/wuying-alpha-service/conf/circus.ini
```

#### 问题2: 多个Python进程
```bash
# 查看所有Python进程
ps aux | grep "start_service.py"

# 手动停止多余进程
kill -9 <进程ID>
```

#### 问题3: 启动脚本执行失败
```bash
# 检查启动脚本是否存在
ls -la $ADMIN_HOME/schedulerxAgent/bin/start.sh

# 手动执行启动脚本测试
export JAVA_HOME=/opt/taobao/java
export PATH=$JAVA_HOME/bin:$PATH
java -version
sh $ADMIN_HOME/schedulerxAgent/bin/start.sh
```

#### 问题4: 健康检查失败
```bash
# 检查端口是否监听
netstat -tlnp | grep :8000

# 检查服务是否响应
curl -v http://127.0.0.1:8000/status.taobao
```

### 5. 预防措施

#### 监控告警
- 使用监控脚本持续观察服务状态
- 设置关键指标告警（CPU、内存、磁盘等）
- 监控日志中的错误模式

#### 日志分析
- 定期分析错误日志
- 关注RocketMQ连接问题
- 监控异步任务回调异常

#### 系统优化
- 定期清理日志文件
- 监控系统资源使用
- 优化数据库连接池配置

## 工具使用说明

### 快速诊断工具 (`tools/quick_diagnosis.py`)
- **功能**: 快速检查服务状态、进程、日志、系统资源等
- **使用**: `python tools/quick_diagnosis.py`
- **输出**: 详细的诊断报告和建议

### 监控工具 (`tools/monitor_service_restarts.py`)
- **功能**: 持续监控服务状态，分析错误日志
- **使用**: 
  - `python tools/monitor_service_restarts.py status` - 检查当前状态
  - `python tools/monitor_service_restarts.py monitor` - 持续监控
- **输出**: 实时监控报告和错误统计

## 总结

服务重启问题的根本原因是Circus进程管理器的自动重启机制。通过以下措施可以有效解决：

1. **代码优化**: 防止启动脚本重复执行，添加超时控制
2. **监控工具**: 及时发现和诊断问题
3. **手动排查**: 按步骤检查各个组件状态
4. **预防措施**: 持续监控和系统优化

建议使用提供的监控工具持续观察服务状态，及时发现和解决问题。 