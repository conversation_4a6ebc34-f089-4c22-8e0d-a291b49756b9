#!/usr/bin/env python3
"""
服务重启监控脚本
用于监控和分析Alpha Service的重启情况
"""

import os
import sys
import time
import subprocess
import psutil
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import json

class ServiceRestartMonitor:
    """服务重启监控器"""
    
    def __init__(self):
        self.log_file = "/home/<USER>/wuying-alpha-service/logs/application.log"
        self.error_log_file = "/home/<USER>/wuying-alpha-service/logs/error.log"
        self.circus_pid_file = "/home/<USER>/circus.pid"
        self.app_online_file = "/home/<USER>/app.online"
        
    def check_service_status(self) -> Dict[str, any]:
        """检查服务状态"""
        status = {
            "timestamp": datetime.now().isoformat(),
            "circus_running": False,
            "python_processes": [],
            "app_online": False,
            "health_check": False,
            "recent_restarts": 0,
            "error_count": 0
        }
        
        # 检查Circus进程
        try:
            if os.path.exists(self.circus_pid_file):
                with open(self.circus_pid_file, 'r') as f:
                    circus_pid = int(f.read().strip())
                if psutil.pid_exists(circus_pid):
                    status["circus_running"] = True
        except Exception as e:
            print(f"检查Circus进程失败: {e}")
        
        # 检查Python进程
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time']):
                if proc.info['name'] == 'python3' and proc.info['cmdline']:
                    cmdline = ' '.join(proc.info['cmdline'])
                    if 'start_service.py' in cmdline:
                        status["python_processes"].append({
                            "pid": proc.info['pid'],
                            "create_time": datetime.fromtimestamp(proc.info['create_time']).isoformat(),
                            "cmdline": cmdline
                        })
        except Exception as e:
            print(f"检查Python进程失败: {e}")
        
        # 检查app.online文件
        if os.path.exists(self.app_online_file):
            try:
                with open(self.app_online_file, 'r') as f:
                    content = f.read().strip()
                    status["app_online"] = (content == "success")
            except Exception as e:
                print(f"读取app.online文件失败: {e}")
        
        # 检查健康检查
        try:
            result = subprocess.run(
                ["curl", "-s", "-o", "/dev/null", "-w", "%{http_code}", "http://127.0.0.1:8000/status.taobao"],
                capture_output=True,
                text=True,
                timeout=5
            )
            if result.returncode == 0 and result.stdout.strip() == "200":
                status["health_check"] = True
        except Exception as e:
            print(f"健康检查失败: {e}")
        
        return status
    
    def analyze_recent_logs(self, hours: int = 1) -> Dict[str, any]:
        """分析最近的日志"""
        analysis = {
            "startup_script_errors": 0,
            "rocketmq_errors": 0,
            "callback_errors": 0,
            "restart_indicators": 0,
            "error_messages": []
        }
        
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        # 分析应用日志
        if os.path.exists(self.log_file):
            try:
                with open(self.log_file, 'r') as f:
                    for line in f:
                        if self._parse_log_timestamp(line) >= cutoff_time:
                            if "启动后脚本执行失败" in line:
                                analysis["startup_script_errors"] += 1
                                analysis["error_messages"].append(line.strip())
                            elif "RocketMQ" in line or "rocketmq" in line.lower():
                                analysis["rocketmq_errors"] += 1
                                analysis["error_messages"].append(line.strip())
                            elif "callback" in line.lower() and "exception" in line.lower():
                                analysis["callback_errors"] += 1
                                analysis["error_messages"].append(line.strip())
                            elif "启动Pythonic Alpha Service" in line:
                                analysis["restart_indicators"] += 1
            except Exception as e:
                print(f"分析应用日志失败: {e}")
        
        # 分析错误日志
        if os.path.exists(self.error_log_file):
            try:
                with open(self.error_log_file, 'r') as f:
                    for line in f:
                        if self._parse_log_timestamp(line) >= cutoff_time:
                            if "exception" in line.lower():
                                analysis["error_messages"].append(line.strip())
            except Exception as e:
                print(f"分析错误日志失败: {e}")
        
        return analysis
    
    def _parse_log_timestamp(self, log_line: str) -> datetime:
        """解析日志时间戳"""
        try:
            # 假设日志格式: 2025-08-06 16:40:36 | ERROR | ...
            timestamp_str = log_line.split(' | ')[0]
            return datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
        except:
            return datetime.min
    
    def get_recommendations(self, status: Dict, analysis: Dict) -> List[str]:
        """获取建议"""
        recommendations = []
        
        if not status["circus_running"]:
            recommendations.append("❌ Circus进程管理器未运行，需要启动: circusd /home/<USER>/wuying-alpha-service/conf/circus.ini")
        
        if not status["python_processes"]:
            recommendations.append("❌ 没有找到Python服务进程，服务可能已停止")
        
        if len(status["python_processes"]) > 1:
            recommendations.append("⚠️ 发现多个Python进程，可能存在进程泄漏")
        
        if not status["health_check"]:
            recommendations.append("❌ 健康检查失败，服务可能不可用")
        
        if analysis["startup_script_errors"] > 0:
            recommendations.append(f"⚠️ 发现 {analysis['startup_script_errors']} 个启动脚本错误")
        
        if analysis["rocketmq_errors"] > 0:
            recommendations.append(f"⚠️ 发现 {analysis['rocketmq_errors']} 个RocketMQ相关错误")
        
        if analysis["callback_errors"] > 0:
            recommendations.append(f"⚠️ 发现 {analysis['callback_errors']} 个异步回调错误")
        
        if analysis["restart_indicators"] > 1:
            recommendations.append(f"⚠️ 检测到 {analysis['restart_indicators']} 次服务重启")
        
        if not recommendations:
            recommendations.append("✅ 服务状态正常")
        
        return recommendations
    
    def monitor_continuously(self, interval: int = 30):
        """持续监控"""
        print(f"🔍 开始持续监控，检查间隔: {interval}秒")
        print("按 Ctrl+C 停止监控")
        
        try:
            while True:
                print(f"\n{'='*60}")
                print(f"📊 监控报告 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"{'='*60}")
                
                status = self.check_service_status()
                analysis = self.analyze_recent_logs(hours=1)
                recommendations = self.get_recommendations(status, analysis)
                
                # 显示状态
                print(f"🔄 Circus进程: {'✅ 运行中' if status['circus_running'] else '❌ 未运行'}")
                print(f"🐍 Python进程数: {len(status['python_processes'])}")
                print(f"📱 应用在线: {'✅ 是' if status['app_online'] else '❌ 否'}")
                print(f"💚 健康检查: {'✅ 通过' if status['health_check'] else '❌ 失败'}")
                
                # 显示错误统计
                print(f"\n📈 最近1小时错误统计:")
                print(f"   - 启动脚本错误: {analysis['startup_script_errors']}")
                print(f"   - RocketMQ错误: {analysis['rocketmq_errors']}")
                print(f"   - 回调错误: {analysis['callback_errors']}")
                print(f"   - 重启指示: {analysis['restart_indicators']}")
                
                # 显示建议
                print(f"\n💡 建议:")
                for rec in recommendations:
                    print(f"   {rec}")
                
                # 显示最近的错误消息
                if analysis['error_messages']:
                    print(f"\n🚨 最近的错误消息:")
                    for msg in analysis['error_messages'][-3:]:  # 只显示最近3条
                        print(f"   {msg}")
                
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print("\n🛑 监控已停止")

def main():
    """主函数"""
    monitor = ServiceRestartMonitor()
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "status":
            # 单次状态检查
            status = monitor.check_service_status()
            analysis = monitor.analyze_recent_logs(hours=1)
            recommendations = monitor.get_recommendations(status, analysis)
            
            print(json.dumps({
                "status": status,
                "analysis": analysis,
                "recommendations": recommendations
            }, indent=2, ensure_ascii=False))
            
        elif sys.argv[1] == "monitor":
            # 持续监控
            interval = int(sys.argv[2]) if len(sys.argv) > 2 else 30
            monitor.monitor_continuously(interval)
            
        else:
            print("用法:")
            print("  python monitor_service_restarts.py status    # 检查当前状态")
            print("  python monitor_service_restarts.py monitor   # 持续监控")
            print("  python monitor_service_restarts.py monitor 60  # 每60秒检查一次")
    else:
        # 默认显示状态
        status = monitor.check_service_status()
        analysis = monitor.analyze_recent_logs(hours=1)
        recommendations = monitor.get_recommendations(status, analysis)
        
        print("🔍 Alpha Service 重启监控报告")
        print("=" * 50)
        print(f"📅 检查时间: {status['timestamp']}")
        print(f"🔄 Circus进程: {'✅ 运行中' if status['circus_running'] else '❌ 未运行'}")
        print(f"🐍 Python进程数: {len(status['python_processes'])}")
        print(f"📱 应用在线: {'✅ 是' if status['app_online'] else '❌ 否'}")
        print(f"💚 健康检查: {'✅ 通过' if status['health_check'] else '❌ 失败'}")
        
        print(f"\n📈 最近1小时错误统计:")
        print(f"   - 启动脚本错误: {analysis['startup_script_errors']}")
        print(f"   - RocketMQ错误: {analysis['rocketmq_errors']}")
        print(f"   - 回调错误: {analysis['callback_errors']}")
        print(f"   - 重启指示: {analysis['restart_indicators']}")
        
        print(f"\n💡 建议:")
        for rec in recommendations:
            print(f"   {rec}")

if __name__ == "__main__":
    main() 