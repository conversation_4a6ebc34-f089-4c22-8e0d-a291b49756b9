#!/usr/bin/env python3
"""
快速诊断脚本
用于快速分析Alpha Service的重启问题
"""

import os
import sys
import subprocess
from datetime import datetime, timedelta

def check_circus_status():
    """检查Circus状态"""
    print("🔍 检查Circus进程管理器状态...")
    
    try:
        # 检查circus进程
        result = subprocess.run(["ps", "aux"], capture_output=True, text=True)
        circus_processes = [line for line in result.stdout.split('\n') if 'circusd' in line]
        
        if circus_processes:
            print("✅ Circus进程管理器正在运行:")
            for proc in circus_processes:
                print(f"   {proc}")
        else:
            print("❌ Circus进程管理器未运行")
            
        # 检查circus配置文件
        circus_config = "/home/<USER>/wuying-alpha-service/conf/circus.ini"
        if os.path.exists(circus_config):
            print(f"✅ Circus配置文件存在: {circus_config}")
        else:
            print(f"❌ Circus配置文件不存在: {circus_config}")
            
    except Exception as e:
        print(f"❌ 检查Circus状态失败: {e}")

def check_python_processes():
    """检查Python进程"""
    print("\n🔍 检查Python进程...")
    
    try:
        result = subprocess.run(["ps", "aux"], capture_output=True, text=True)
        python_processes = [line for line in result.stdout.split('\n') if 'python3' in line and 'start_service.py' in line]
        
        if python_processes:
            print(f"✅ 发现 {len(python_processes)} 个Python服务进程:")
            for proc in python_processes:
                print(f"   {proc}")
        else:
            print("❌ 没有找到Python服务进程")
            
    except Exception as e:
        print(f"❌ 检查Python进程失败: {e}")

def check_health_status():
    """检查健康状态"""
    print("\n🔍 检查服务健康状态...")
    
    try:
        # 检查app.online文件
        app_online_file = "/home/<USER>/app.online"
        if os.path.exists(app_online_file):
            with open(app_online_file, 'r') as f:
                content = f.read().strip()
            print(f"📱 app.online文件: {content}")
        else:
            print("❌ app.online文件不存在")
        
        # 检查HTTP健康检查
        result = subprocess.run(
            ["curl", "-s", "http://127.0.0.1:8000/status.taobao"],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if result.returncode == 0:
            print(f"💚 HTTP健康检查: {result.stdout.strip()}")
        else:
            print(f"❌ HTTP健康检查失败: {result.stderr}")
            
    except Exception as e:
        print(f"❌ 检查健康状态失败: {e}")

def check_recent_logs():
    """检查最近的日志"""
    print("\n🔍 检查最近的日志...")
    
    log_files = [
        "/home/<USER>/wuying-alpha-service/logs/application.log",
        "/home/<USER>/wuying-alpha-service/logs/error.log"
    ]
    
    for log_file in log_files:
        if os.path.exists(log_file):
            print(f"\n📄 分析日志文件: {log_file}")
            
            try:
                # 获取文件最后修改时间
                mtime = datetime.fromtimestamp(os.path.getmtime(log_file))
                print(f"   最后修改时间: {mtime}")
                
                # 读取最后10行
                with open(log_file, 'r') as f:
                    lines = f.readlines()
                    last_lines = lines[-10:] if len(lines) > 10 else lines
                    
                print(f"   最后 {len(last_lines)} 行日志:")
                for line in last_lines:
                    line = line.strip()
                    if line:
                        # 高亮错误信息
                        if "ERROR" in line or "exception" in line.lower():
                            print(f"   ❌ {line}")
                        elif "启动后脚本执行失败" in line:
                            print(f"   ⚠️ {line}")
                        else:
                            print(f"   ℹ️ {line}")
                            
            except Exception as e:
                print(f"   ❌ 读取日志文件失败: {e}")
        else:
            print(f"❌ 日志文件不存在: {log_file}")

def check_system_resources():
    """检查系统资源"""
    print("\n🔍 检查系统资源...")
    
    try:
        # 检查内存使用
        result = subprocess.run(["free", "-h"], capture_output=True, text=True)
        print("💾 内存使用情况:")
        for line in result.stdout.split('\n'):
            if line.strip():
                print(f"   {line}")
        
        # 检查磁盘使用
        result = subprocess.run(["df", "-h"], capture_output=True, text=True)
        print("\n💿 磁盘使用情况:")
        for line in result.stdout.split('\n'):
            if line.strip():
                print(f"   {line}")
                
        # 检查负载
        result = subprocess.run(["uptime"], capture_output=True, text=True)
        print(f"\n⚡ 系统负载: {result.stdout.strip()}")
        
    except Exception as e:
        print(f"❌ 检查系统资源失败: {e}")

def check_network_connectivity():
    """检查网络连接"""
    print("\n🔍 检查网络连接...")
    
    try:
        # 检查本地端口
        result = subprocess.run(["netstat", "-tlnp"], capture_output=True, text=True)
        port_8000 = [line for line in result.stdout.split('\n') if ':8000' in line]
        
        if port_8000:
            print("✅ 端口8000正在监听:")
            for line in port_8000:
                print(f"   {line}")
        else:
            print("❌ 端口8000未监听")
            
        # 检查外部连接
        test_urls = [
            "http://127.0.0.1:8000/status.taobao",
            "http://localhost:8000/status.taobao"
        ]
        
        for url in test_urls:
            try:
                result = subprocess.run(
                    ["curl", "-s", "-o", "/dev/null", "-w", "%{http_code}", url],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                if result.returncode == 0:
                    print(f"✅ {url}: HTTP {result.stdout.strip()}")
                else:
                    print(f"❌ {url}: 连接失败")
            except Exception as e:
                print(f"❌ {url}: {e}")
                
    except Exception as e:
        print(f"❌ 检查网络连接失败: {e}")

def main():
    """主函数"""
    print("🚀 Alpha Service 快速诊断工具")
    print("=" * 50)
    print(f"📅 诊断时间: {datetime.now()}")
    print("=" * 50)
    
    # 执行各项检查
    check_circus_status()
    check_python_processes()
    check_health_status()
    check_recent_logs()
    check_system_resources()
    check_network_connectivity()
    
    print("\n" + "=" * 50)
    print("💡 诊断完成！")
    print("\n如果发现问题，建议:")
    print("1. 检查Circus进程是否正常运行")
    print("2. 查看错误日志中的具体错误信息")
    print("3. 检查系统资源是否充足")
    print("4. 确认网络连接是否正常")
    print("5. 使用监控脚本持续观察: python tools/monitor_service_restarts.py monitor")

if __name__ == "__main__":
    main() 