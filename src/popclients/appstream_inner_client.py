# -*- coding: utf-8 -*-
"""
AppStreamInnerClient - 封装阿里云AppStream内部服务客户端
提供简化的接口来访问AppStream内部服务
"""
from typing import Optional, Dict, Any
import sys
import os

# 添加本地appstream-center-inner-20211212包到Python路径
current_dir = os.path.dirname(__file__)
appstream_path = os.path.join(current_dir, 'appstream-center-inner-20211212')
if appstream_path not in sys.path:
    sys.path.insert(0, appstream_path)

try:
    from alibabacloud_tea_openapi import models as open_api_models
    from alibabacloud_appstream_center_inner20211212 import client
    from alibabacloud_appstream_center_inner20211212 import models as appstream_models
except ImportError:
    # 如果无法导入，尝试从本地路径导入
    try:
        from alibabacloud_tea_openapi import models as open_api_models
    except ImportError:
        # 如果还是无法导入，创建一个简单的模拟
        class MockConfig:
            def __init__(self, **kwargs):
                for k, v in kwargs.items():
                    setattr(self, k, v)

        class MockModels:
            Config = MockConfig

        open_api_models = MockModels()

    # 导入本地的appstream_center_inner20211212
    from alibabacloud_appstream_center_inner20211212 import client
    from alibabacloud_appstream_center_inner20211212 import models as appstream_models

from aliyunaklesscredprovider.core import AklessCredproviderFactory
from loguru import logger


class AppStreamInnerClient:
    """
    AppStream内部服务客户端封装类
    提供简化的接口来访问AppStream内部服务
    """
    
    def __init__(
        self,
        endpoint: Optional[str] = None,
        connect_timeout: int = 5000,
        read_timeout: int = 10000,
        **kwargs
    ):
        """
        初始化AppStream内部服务客户端

        Args:
            endpoint: 服务端点，默认为AppStream内部服务端点
            connect_timeout: 连接超时时间（毫秒）
            read_timeout: 读取超时时间（毫秒）
            **kwargs: 其他配置参数
        """
        # 从配置中读取RAM角色ARN
        try:
            # 尝试多种导入方式
            try:
                from src.shared.config.environments import env_manager
            except ImportError:
                import sys
                import os
                # 添加项目根目录到Python路径
                project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
                if project_root not in sys.path:
                    sys.path.insert(0, project_root)
                from src.shared.config.environments import env_manager

            config = env_manager.get_config()
            ram_role_arn = config.ram_role_arn
            region_id = config.region_id

            if not ram_role_arn:
                raise ValueError("配置中缺少ram_role_arn，无法使用无AK认证")

            logger.info(f"从配置中读取RAM角色ARN: {ram_role_arn} - 环境: {env_manager.current_env.value}")
        except Exception as e:
            logger.error(f"无法从配置中读取RAM角色ARN: {e}")
            raise AppStreamInnerClientError(f"无法从配置中读取RAM角色ARN: {e}")

        # 设置默认端点
        if not endpoint:
            endpoint = "appstream-center-inner-share.cn-hangzhou.aliyuncs.com"

        self.endpoint = endpoint
        self.connect_timeout = connect_timeout
        self.read_timeout = read_timeout
        self.ram_role_arn = ram_role_arn
        self.region_id = region_id

        # 使用无AK认证创建凭证
        try:
            cred_client = AklessCredproviderFactory.get_opensdk_v2_credential(ram_role_arn)
        except Exception as e:
            logger.error(f"创建无AK凭证失败: {e}")
            raise AppStreamInnerClientError(f"创建无AK凭证失败: {e}")

        # 创建配置
        self.config = open_api_models.Config(
            credential=cred_client,
            region_id=region_id,
            connect_timeout=connect_timeout,
            read_timeout=read_timeout,
            **kwargs
        )
        # 确保端点被正确设置
        self.config.endpoint = endpoint

        # 初始化客户端
        self._client = client.Client(self.config)

        # 验证端点设置
        logger.info(f"AppStream内部客户端端点设置: {self.config.endpoint}")
        logger.info(f"AppStream内部客户端实际端点: {getattr(self._client, '_endpoint', 'Unknown')}")
    
    def get_sts_token(
        self,
        account_type: Optional[str] = None,
        auto_create_user: Optional[bool] = None,
        end_user_id: Optional[str] = None,
        external_user_id: Optional[str] = None,
        policy: Optional[str] = None,
        user_ali_uid: Optional[int] = None,
    ) -> appstream_models.GetStsTokenResponse:
        """
        获取STS Token
        
        Args:
            account_type: 账户类型
            auto_create_user: 是否自动创建用户
            end_user_id: 终端用户ID
            external_user_id: 外部用户ID
            policy: 策略
            user_ali_uid: 用户阿里云UID
            
        Returns:
            GetStsTokenResponse: STS Token响应
        """
        try:
            request = appstream_models.GetStsTokenRequest(
                account_type=account_type,
                auto_create_user=auto_create_user,
                end_user_id=end_user_id,
                external_user_id=external_user_id,
                policy=policy,
                user_ali_uid=user_ali_uid
            )
            
            logger.debug(f"获取STS Token请求: end_user_id={end_user_id}, external_user_id={external_user_id}")
            response = self._client.get_sts_token(request)
            logger.debug(f"获取STS Token成功: request_id={response.body.request_id if response.body else 'Unknown'}")
            
            return response
        except Exception as e:
            logger.error(f"获取STS Token失败: {str(e)}")
            raise AppStreamInnerClientError(f"获取STS Token失败: {str(e)}") from e

    async def get_sts_token_async(
        self,
        account_type: Optional[str] = None,
        auto_create_user: Optional[bool] = None,
        end_user_id: Optional[str] = None,
        external_user_id: Optional[str] = None,
        policy: Optional[str] = None,
        user_ali_uid: Optional[int] = None,
    ) -> appstream_models.GetStsTokenResponse:
        """
        异步获取STS Token
        
        Args:
            account_type: 账户类型
            auto_create_user: 是否自动创建用户
            end_user_id: 终端用户ID
            external_user_id: 外部用户ID
            policy: 策略
            user_ali_uid: 用户阿里云UID
            
        Returns:
            GetStsTokenResponse: STS Token响应
        """
        try:
            request = appstream_models.GetStsTokenRequest(
                account_type=account_type,
                auto_create_user=auto_create_user,
                end_user_id=end_user_id,
                external_user_id=external_user_id,
                policy=policy,
                user_ali_uid=user_ali_uid
            )
            
            logger.debug(f"异步获取STS Token请求: end_user_id={end_user_id}, external_user_id={external_user_id}")
            response = await self._client.get_sts_token_async(request)
            logger.debug(f"异步获取STS Token成功: request_id={response.body.request_id if response.body else 'Unknown'}")
            
            return response
        except Exception as e:
            logger.error(f"异步获取STS Token失败: {str(e)}")
            raise AppStreamInnerClientError(f"异步获取STS Token失败: {str(e)}") from e

    # 实用方法
    def get_client_info(self) -> Dict[str, Any]:
        """
        获取客户端信息

        Returns:
            Dict[str, Any]: 客户端配置信息
        """
        return {
            "ram_role_arn": self.ram_role_arn,
            "region_id": self.region_id,
            "endpoint": self.endpoint or "appstream-center-inner.aliyuncs.com",
            "connect_timeout": self.connect_timeout,
            "read_timeout": self.read_timeout
        }

    def __str__(self) -> str:
        """返回客户端字符串表示"""
        return f"AppStreamInnerClient(endpoint={self.endpoint}, ram_role_arn={self.ram_role_arn})"

    def __repr__(self) -> str:
        """返回客户端详细字符串表示"""
        return self.__str__()


class AppStreamInnerClientError(Exception):
    """AppStreamInnerClient异常类"""
    pass


# 全局单例实例
_appstream_inner_client_instance = None


def get_appstream_inner_client(
    endpoint: Optional[str] = None,
    **kwargs
) -> AppStreamInnerClient:
    """
    获取AppStream内部服务客户端单例实例

    Args:
        endpoint: 服务端点
        **kwargs: 其他配置参数

    Returns:
        AppStreamInnerClient: AppStream内部服务客户端实例
    """
    global _appstream_inner_client_instance

    if _appstream_inner_client_instance is None:
        _appstream_inner_client_instance = AppStreamInnerClient(
            endpoint=endpoint,
            **kwargs
        )

    return _appstream_inner_client_instance


def reset_appstream_inner_client():
    """重置AppStream内部服务客户端单例实例"""
    global _appstream_inner_client_instance
    _appstream_inner_client_instance = None
