# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from alibabacloud_tea_openapi.client import Client as OpenApiClient 
from alibabacloud_tea_openapi import utils_models as open_api_util_models 
from darabonba.core import <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON> 
from alibabacloud_tea_openapi.utils import Utils 
from alibabacloud_eds_storage_inner20230117 import models as main_models 
from darabonba.runtime import RuntimeOptions 
from typing import Dict


"""
"""
class Client(OpenApiClient):

    def __init__(
        self,
        config: open_api_util_models.Config,
    ):
        super().__init__(config)
        self._endpoint_rule = ''
        self.check_config(config)
        self._endpoint = self.get_endpoint('eds-storage-inner', self._region_id, self._endpoint_rule, self._network, self._suffix, self._endpoint_map, self._endpoint)

    def get_endpoint(
        self,
        product_id: str,
        region_id: str,
        endpoint_rule: str,
        network: str,
        suffix: str,
        endpoint_map: Dict[str, str],
        endpoint: str,
    ) -> str:
        if not DaraCore.is_null(endpoint):
            return endpoint
        if not DaraCore.is_null(endpoint_map) and not DaraCore.is_null(endpoint_map.get(region_id)):
            return endpoint_map.get(region_id)
        return Utils.get_endpoint_rules(product_id, region_id, endpoint_rule, network, suffix)

    def add_peripheral_driver_with_options(
        self,
        request: main_models.AddPeripheralDriverRequest,
        runtime: RuntimeOptions,
    ) -> main_models.AddPeripheralDriverResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.brand):
            query['Brand'] = request.brand
        if not DaraCore.is_null(request.driver_id):
            query['DriverId'] = request.driver_id
        if not DaraCore.is_null(request.driver_name):
            query['DriverName'] = request.driver_name
        if not DaraCore.is_null(request.file_path):
            query['FilePath'] = request.file_path
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'AddPeripheralDriver',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.AddPeripheralDriverResponse(),
            self.call_api(params, req, runtime)
        )

    async def add_peripheral_driver_with_options_async(
        self,
        request: main_models.AddPeripheralDriverRequest,
        runtime: RuntimeOptions,
    ) -> main_models.AddPeripheralDriverResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.brand):
            query['Brand'] = request.brand
        if not DaraCore.is_null(request.driver_id):
            query['DriverId'] = request.driver_id
        if not DaraCore.is_null(request.driver_name):
            query['DriverName'] = request.driver_name
        if not DaraCore.is_null(request.file_path):
            query['FilePath'] = request.file_path
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'AddPeripheralDriver',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.AddPeripheralDriverResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def add_peripheral_driver(
        self,
        request: main_models.AddPeripheralDriverRequest,
    ) -> main_models.AddPeripheralDriverResponse:
        runtime = RuntimeOptions()
        return self.add_peripheral_driver_with_options(request, runtime)

    async def add_peripheral_driver_async(
        self,
        request: main_models.AddPeripheralDriverRequest,
    ) -> main_models.AddPeripheralDriverResponse:
        runtime = RuntimeOptions()
        return await self.add_peripheral_driver_with_options_async(request, runtime)

    def check_ad_pds_user_with_options(
        self,
        request: main_models.CheckAdPdsUserRequest,
        runtime: RuntimeOptions,
    ) -> main_models.CheckAdPdsUserResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.ali_uid):
            query['AliUid'] = request.ali_uid
        if not DaraCore.is_null(request.domain_name):
            query['DomainName'] = request.domain_name
        if not DaraCore.is_null(request.end_user_id):
            query['EndUserId'] = request.end_user_id
        if not DaraCore.is_null(request.region_id):
            query['RegionId'] = request.region_id
        if not DaraCore.is_null(request.sid):
            query['Sid'] = request.sid
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'CheckAdPdsUser',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.CheckAdPdsUserResponse(),
            self.call_api(params, req, runtime)
        )

    async def check_ad_pds_user_with_options_async(
        self,
        request: main_models.CheckAdPdsUserRequest,
        runtime: RuntimeOptions,
    ) -> main_models.CheckAdPdsUserResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.ali_uid):
            query['AliUid'] = request.ali_uid
        if not DaraCore.is_null(request.domain_name):
            query['DomainName'] = request.domain_name
        if not DaraCore.is_null(request.end_user_id):
            query['EndUserId'] = request.end_user_id
        if not DaraCore.is_null(request.region_id):
            query['RegionId'] = request.region_id
        if not DaraCore.is_null(request.sid):
            query['Sid'] = request.sid
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'CheckAdPdsUser',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.CheckAdPdsUserResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def check_ad_pds_user(
        self,
        request: main_models.CheckAdPdsUserRequest,
    ) -> main_models.CheckAdPdsUserResponse:
        runtime = RuntimeOptions()
        return self.check_ad_pds_user_with_options(request, runtime)

    async def check_ad_pds_user_async(
        self,
        request: main_models.CheckAdPdsUserRequest,
    ) -> main_models.CheckAdPdsUserResponse:
        runtime = RuntimeOptions()
        return await self.check_ad_pds_user_with_options_async(request, runtime)

    def complete_upload_file_with_options(
        self,
        request: main_models.CompleteUploadFileRequest,
        runtime: RuntimeOptions,
    ) -> main_models.CompleteUploadFileResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.file_id):
            query['FileId'] = request.file_id
        if not DaraCore.is_null(request.product_type):
            query['ProductType'] = request.product_type
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        if not DaraCore.is_null(request.wy_drive_owner_id):
            query['WyDriveOwnerId'] = request.wy_drive_owner_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'CompleteUploadFile',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.CompleteUploadFileResponse(),
            self.call_api(params, req, runtime)
        )

    async def complete_upload_file_with_options_async(
        self,
        request: main_models.CompleteUploadFileRequest,
        runtime: RuntimeOptions,
    ) -> main_models.CompleteUploadFileResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.file_id):
            query['FileId'] = request.file_id
        if not DaraCore.is_null(request.product_type):
            query['ProductType'] = request.product_type
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        if not DaraCore.is_null(request.wy_drive_owner_id):
            query['WyDriveOwnerId'] = request.wy_drive_owner_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'CompleteUploadFile',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.CompleteUploadFileResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def complete_upload_file(
        self,
        request: main_models.CompleteUploadFileRequest,
    ) -> main_models.CompleteUploadFileResponse:
        runtime = RuntimeOptions()
        return self.complete_upload_file_with_options(request, runtime)

    async def complete_upload_file_async(
        self,
        request: main_models.CompleteUploadFileRequest,
    ) -> main_models.CompleteUploadFileResponse:
        runtime = RuntimeOptions()
        return await self.complete_upload_file_with_options_async(request, runtime)

    def create_gray_versions_with_options(
        self,
        request: main_models.CreateGrayVersionsRequest,
        runtime: RuntimeOptions,
    ) -> main_models.CreateGrayVersionsResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        if not DaraCore.is_null(request.versions):
            query['Versions'] = request.versions
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'CreateGrayVersions',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.CreateGrayVersionsResponse(),
            self.call_api(params, req, runtime)
        )

    async def create_gray_versions_with_options_async(
        self,
        request: main_models.CreateGrayVersionsRequest,
        runtime: RuntimeOptions,
    ) -> main_models.CreateGrayVersionsResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        if not DaraCore.is_null(request.versions):
            query['Versions'] = request.versions
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'CreateGrayVersions',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.CreateGrayVersionsResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def create_gray_versions(
        self,
        request: main_models.CreateGrayVersionsRequest,
    ) -> main_models.CreateGrayVersionsResponse:
        runtime = RuntimeOptions()
        return self.create_gray_versions_with_options(request, runtime)

    async def create_gray_versions_async(
        self,
        request: main_models.CreateGrayVersionsRequest,
    ) -> main_models.CreateGrayVersionsResponse:
        runtime = RuntimeOptions()
        return await self.create_gray_versions_with_options_async(request, runtime)

    def describe_file_transfer_approval_config_with_options(
        self,
        request: main_models.DescribeFileTransferApprovalConfigRequest,
        runtime: RuntimeOptions,
    ) -> main_models.DescribeFileTransferApprovalConfigResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.account_type):
            query['AccountType'] = request.account_type
        if not DaraCore.is_null(request.ad_domain):
            query['AdDomain'] = request.ad_domain
        if not DaraCore.is_null(request.scene):
            query['Scene'] = request.scene
        if not DaraCore.is_null(request.task_type):
            query['TaskType'] = request.task_type
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        if not DaraCore.is_null(request.end_user_id):
            query['endUserId'] = request.end_user_id
        if not DaraCore.is_null(request.wy_id):
            query['wyId'] = request.wy_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'DescribeFileTransferApprovalConfig',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.DescribeFileTransferApprovalConfigResponse(),
            self.call_api(params, req, runtime)
        )

    async def describe_file_transfer_approval_config_with_options_async(
        self,
        request: main_models.DescribeFileTransferApprovalConfigRequest,
        runtime: RuntimeOptions,
    ) -> main_models.DescribeFileTransferApprovalConfigResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.account_type):
            query['AccountType'] = request.account_type
        if not DaraCore.is_null(request.ad_domain):
            query['AdDomain'] = request.ad_domain
        if not DaraCore.is_null(request.scene):
            query['Scene'] = request.scene
        if not DaraCore.is_null(request.task_type):
            query['TaskType'] = request.task_type
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        if not DaraCore.is_null(request.end_user_id):
            query['endUserId'] = request.end_user_id
        if not DaraCore.is_null(request.wy_id):
            query['wyId'] = request.wy_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'DescribeFileTransferApprovalConfig',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.DescribeFileTransferApprovalConfigResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def describe_file_transfer_approval_config(
        self,
        request: main_models.DescribeFileTransferApprovalConfigRequest,
    ) -> main_models.DescribeFileTransferApprovalConfigResponse:
        runtime = RuntimeOptions()
        return self.describe_file_transfer_approval_config_with_options(request, runtime)

    async def describe_file_transfer_approval_config_async(
        self,
        request: main_models.DescribeFileTransferApprovalConfigRequest,
    ) -> main_models.DescribeFileTransferApprovalConfigResponse:
        runtime = RuntimeOptions()
        return await self.describe_file_transfer_approval_config_with_options_async(request, runtime)

    def describe_file_transfer_approval_configs_with_options(
        self,
        tmp_req: main_models.DescribeFileTransferApprovalConfigsRequest,
        runtime: RuntimeOptions,
    ) -> main_models.DescribeFileTransferApprovalConfigsResponse:
        tmp_req.validate()
        request = main_models.DescribeFileTransferApprovalConfigsShrinkRequest()
        Utils.convert(tmp_req, request)
        if not DaraCore.is_null(tmp_req.user_infos):
            request.user_infos_shrink = Utils.array_to_string_with_specified_style(tmp_req.user_infos, 'UserInfos', 'json')
        query = {}
        if not DaraCore.is_null(request.account_type):
            query['AccountType'] = request.account_type
        if not DaraCore.is_null(request.ad_domain):
            query['AdDomain'] = request.ad_domain
        if not DaraCore.is_null(request.scene):
            query['Scene'] = request.scene
        if not DaraCore.is_null(request.task_type):
            query['TaskType'] = request.task_type
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        if not DaraCore.is_null(request.user_groups):
            query['UserGroups'] = request.user_groups
        if not DaraCore.is_null(request.user_infos_shrink):
            query['UserInfos'] = request.user_infos_shrink
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'DescribeFileTransferApprovalConfigs',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.DescribeFileTransferApprovalConfigsResponse(),
            self.call_api(params, req, runtime)
        )

    async def describe_file_transfer_approval_configs_with_options_async(
        self,
        tmp_req: main_models.DescribeFileTransferApprovalConfigsRequest,
        runtime: RuntimeOptions,
    ) -> main_models.DescribeFileTransferApprovalConfigsResponse:
        tmp_req.validate()
        request = main_models.DescribeFileTransferApprovalConfigsShrinkRequest()
        Utils.convert(tmp_req, request)
        if not DaraCore.is_null(tmp_req.user_infos):
            request.user_infos_shrink = Utils.array_to_string_with_specified_style(tmp_req.user_infos, 'UserInfos', 'json')
        query = {}
        if not DaraCore.is_null(request.account_type):
            query['AccountType'] = request.account_type
        if not DaraCore.is_null(request.ad_domain):
            query['AdDomain'] = request.ad_domain
        if not DaraCore.is_null(request.scene):
            query['Scene'] = request.scene
        if not DaraCore.is_null(request.task_type):
            query['TaskType'] = request.task_type
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        if not DaraCore.is_null(request.user_groups):
            query['UserGroups'] = request.user_groups
        if not DaraCore.is_null(request.user_infos_shrink):
            query['UserInfos'] = request.user_infos_shrink
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'DescribeFileTransferApprovalConfigs',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.DescribeFileTransferApprovalConfigsResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def describe_file_transfer_approval_configs(
        self,
        request: main_models.DescribeFileTransferApprovalConfigsRequest,
    ) -> main_models.DescribeFileTransferApprovalConfigsResponse:
        runtime = RuntimeOptions()
        return self.describe_file_transfer_approval_configs_with_options(request, runtime)

    async def describe_file_transfer_approval_configs_async(
        self,
        request: main_models.DescribeFileTransferApprovalConfigsRequest,
    ) -> main_models.DescribeFileTransferApprovalConfigsResponse:
        runtime = RuntimeOptions()
        return await self.describe_file_transfer_approval_configs_with_options_async(request, runtime)

    def describe_global_cloud_drive_services_with_options(
        self,
        request: main_models.DescribeGlobalCloudDriveServicesRequest,
        runtime: RuntimeOptions,
    ) -> main_models.DescribeGlobalCloudDriveServicesResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.ali_uid):
            query['AliUid'] = request.ali_uid
        if not DaraCore.is_null(request.biz_type):
            query['BizType'] = request.biz_type
        if not DaraCore.is_null(request.cds_id):
            query['CdsId'] = request.cds_id
        if not DaraCore.is_null(request.directory_type):
            query['DirectoryType'] = request.directory_type
        if not DaraCore.is_null(request.domain_name):
            query['DomainName'] = request.domain_name
        if not DaraCore.is_null(request.global_status):
            query['GlobalStatus'] = request.global_status
        if not DaraCore.is_null(request.is_token_needed):
            query['IsTokenNeeded'] = request.is_token_needed
        if not DaraCore.is_null(request.max_results):
            query['MaxResults'] = request.max_results
        if not DaraCore.is_null(request.next_token):
            query['NextToken'] = request.next_token
        if not DaraCore.is_null(request.pds_subdomain_id):
            query['PdsSubdomainId'] = request.pds_subdomain_id
        if not DaraCore.is_null(request.sid):
            query['Sid'] = request.sid
        if not DaraCore.is_null(request.solution_id):
            query['SolutionId'] = request.solution_id
        if not DaraCore.is_null(request.user_region_id):
            query['UserRegionId'] = request.user_region_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'DescribeGlobalCloudDriveServices',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.DescribeGlobalCloudDriveServicesResponse(),
            self.call_api(params, req, runtime)
        )

    async def describe_global_cloud_drive_services_with_options_async(
        self,
        request: main_models.DescribeGlobalCloudDriveServicesRequest,
        runtime: RuntimeOptions,
    ) -> main_models.DescribeGlobalCloudDriveServicesResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.ali_uid):
            query['AliUid'] = request.ali_uid
        if not DaraCore.is_null(request.biz_type):
            query['BizType'] = request.biz_type
        if not DaraCore.is_null(request.cds_id):
            query['CdsId'] = request.cds_id
        if not DaraCore.is_null(request.directory_type):
            query['DirectoryType'] = request.directory_type
        if not DaraCore.is_null(request.domain_name):
            query['DomainName'] = request.domain_name
        if not DaraCore.is_null(request.global_status):
            query['GlobalStatus'] = request.global_status
        if not DaraCore.is_null(request.is_token_needed):
            query['IsTokenNeeded'] = request.is_token_needed
        if not DaraCore.is_null(request.max_results):
            query['MaxResults'] = request.max_results
        if not DaraCore.is_null(request.next_token):
            query['NextToken'] = request.next_token
        if not DaraCore.is_null(request.pds_subdomain_id):
            query['PdsSubdomainId'] = request.pds_subdomain_id
        if not DaraCore.is_null(request.sid):
            query['Sid'] = request.sid
        if not DaraCore.is_null(request.solution_id):
            query['SolutionId'] = request.solution_id
        if not DaraCore.is_null(request.user_region_id):
            query['UserRegionId'] = request.user_region_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'DescribeGlobalCloudDriveServices',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.DescribeGlobalCloudDriveServicesResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def describe_global_cloud_drive_services(
        self,
        request: main_models.DescribeGlobalCloudDriveServicesRequest,
    ) -> main_models.DescribeGlobalCloudDriveServicesResponse:
        runtime = RuntimeOptions()
        return self.describe_global_cloud_drive_services_with_options(request, runtime)

    async def describe_global_cloud_drive_services_async(
        self,
        request: main_models.DescribeGlobalCloudDriveServicesRequest,
    ) -> main_models.DescribeGlobalCloudDriveServicesResponse:
        runtime = RuntimeOptions()
        return await self.describe_global_cloud_drive_services_with_options_async(request, runtime)

    def describe_global_cloud_drives_with_options(
        self,
        request: main_models.DescribeGlobalCloudDrivesRequest,
        runtime: RuntimeOptions,
    ) -> main_models.DescribeGlobalCloudDrivesResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.ali_uid):
            query['AliUid'] = request.ali_uid
        if not DaraCore.is_null(request.directory_id):
            query['DirectoryId'] = request.directory_id
        if not DaraCore.is_null(request.directory_type):
            query['DirectoryType'] = request.directory_type
        if not DaraCore.is_null(request.end_user_id):
            query['EndUserId'] = request.end_user_id
        if not DaraCore.is_null(request.user_region_id):
            query['UserRegionId'] = request.user_region_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'DescribeGlobalCloudDrives',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.DescribeGlobalCloudDrivesResponse(),
            self.call_api(params, req, runtime)
        )

    async def describe_global_cloud_drives_with_options_async(
        self,
        request: main_models.DescribeGlobalCloudDrivesRequest,
        runtime: RuntimeOptions,
    ) -> main_models.DescribeGlobalCloudDrivesResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.ali_uid):
            query['AliUid'] = request.ali_uid
        if not DaraCore.is_null(request.directory_id):
            query['DirectoryId'] = request.directory_id
        if not DaraCore.is_null(request.directory_type):
            query['DirectoryType'] = request.directory_type
        if not DaraCore.is_null(request.end_user_id):
            query['EndUserId'] = request.end_user_id
        if not DaraCore.is_null(request.user_region_id):
            query['UserRegionId'] = request.user_region_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'DescribeGlobalCloudDrives',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.DescribeGlobalCloudDrivesResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def describe_global_cloud_drives(
        self,
        request: main_models.DescribeGlobalCloudDrivesRequest,
    ) -> main_models.DescribeGlobalCloudDrivesResponse:
        runtime = RuntimeOptions()
        return self.describe_global_cloud_drives_with_options(request, runtime)

    async def describe_global_cloud_drives_async(
        self,
        request: main_models.DescribeGlobalCloudDrivesRequest,
    ) -> main_models.DescribeGlobalCloudDrivesResponse:
        runtime = RuntimeOptions()
        return await self.describe_global_cloud_drives_with_options_async(request, runtime)

    def describe_gray_levels_by_resource_groups_with_options(
        self,
        request: main_models.DescribeGrayLevelsByResourceGroupsRequest,
        runtime: RuntimeOptions,
    ) -> main_models.DescribeGrayLevelsByResourceGroupsResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.resource_group_ids):
            query['ResourceGroupIds'] = request.resource_group_ids
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'DescribeGrayLevelsByResourceGroups',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.DescribeGrayLevelsByResourceGroupsResponse(),
            self.call_api(params, req, runtime)
        )

    async def describe_gray_levels_by_resource_groups_with_options_async(
        self,
        request: main_models.DescribeGrayLevelsByResourceGroupsRequest,
        runtime: RuntimeOptions,
    ) -> main_models.DescribeGrayLevelsByResourceGroupsResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.resource_group_ids):
            query['ResourceGroupIds'] = request.resource_group_ids
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'DescribeGrayLevelsByResourceGroups',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.DescribeGrayLevelsByResourceGroupsResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def describe_gray_levels_by_resource_groups(
        self,
        request: main_models.DescribeGrayLevelsByResourceGroupsRequest,
    ) -> main_models.DescribeGrayLevelsByResourceGroupsResponse:
        runtime = RuntimeOptions()
        return self.describe_gray_levels_by_resource_groups_with_options(request, runtime)

    async def describe_gray_levels_by_resource_groups_async(
        self,
        request: main_models.DescribeGrayLevelsByResourceGroupsRequest,
    ) -> main_models.DescribeGrayLevelsByResourceGroupsResponse:
        runtime = RuntimeOptions()
        return await self.describe_gray_levels_by_resource_groups_with_options_async(request, runtime)

    def describe_peripheral_driver_category_with_options(
        self,
        request: main_models.DescribePeripheralDriverCategoryRequest,
        runtime: RuntimeOptions,
    ) -> main_models.DescribePeripheralDriverCategoryResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.device_type):
            query['DeviceType'] = request.device_type
        if not DaraCore.is_null(request.owner_type):
            query['OwnerType'] = request.owner_type
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'DescribePeripheralDriverCategory',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.DescribePeripheralDriverCategoryResponse(),
            self.call_api(params, req, runtime)
        )

    async def describe_peripheral_driver_category_with_options_async(
        self,
        request: main_models.DescribePeripheralDriverCategoryRequest,
        runtime: RuntimeOptions,
    ) -> main_models.DescribePeripheralDriverCategoryResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.device_type):
            query['DeviceType'] = request.device_type
        if not DaraCore.is_null(request.owner_type):
            query['OwnerType'] = request.owner_type
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'DescribePeripheralDriverCategory',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.DescribePeripheralDriverCategoryResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def describe_peripheral_driver_category(
        self,
        request: main_models.DescribePeripheralDriverCategoryRequest,
    ) -> main_models.DescribePeripheralDriverCategoryResponse:
        runtime = RuntimeOptions()
        return self.describe_peripheral_driver_category_with_options(request, runtime)

    async def describe_peripheral_driver_category_async(
        self,
        request: main_models.DescribePeripheralDriverCategoryRequest,
    ) -> main_models.DescribePeripheralDriverCategoryResponse:
        runtime = RuntimeOptions()
        return await self.describe_peripheral_driver_category_with_options_async(request, runtime)

    def describe_peripheral_drivers_with_options(
        self,
        request: main_models.DescribePeripheralDriversRequest,
        runtime: RuntimeOptions,
    ) -> main_models.DescribePeripheralDriversResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.brand):
            query['Brand'] = request.brand
        if not DaraCore.is_null(request.device_type):
            query['DeviceType'] = request.device_type
        if not DaraCore.is_null(request.driver_ids):
            query['DriverIds'] = request.driver_ids
        if not DaraCore.is_null(request.driver_name):
            query['DriverName'] = request.driver_name
        if not DaraCore.is_null(request.filter):
            query['Filter'] = request.filter
        if not DaraCore.is_null(request.include_others):
            query['IncludeOthers'] = request.include_others
        if not DaraCore.is_null(request.lang):
            query['Lang'] = request.lang
        if not DaraCore.is_null(request.owner_type):
            query['OwnerType'] = request.owner_type
        if not DaraCore.is_null(request.page_number):
            query['PageNumber'] = request.page_number
        if not DaraCore.is_null(request.page_size):
            query['PageSize'] = request.page_size
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'DescribePeripheralDrivers',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.DescribePeripheralDriversResponse(),
            self.call_api(params, req, runtime)
        )

    async def describe_peripheral_drivers_with_options_async(
        self,
        request: main_models.DescribePeripheralDriversRequest,
        runtime: RuntimeOptions,
    ) -> main_models.DescribePeripheralDriversResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.brand):
            query['Brand'] = request.brand
        if not DaraCore.is_null(request.device_type):
            query['DeviceType'] = request.device_type
        if not DaraCore.is_null(request.driver_ids):
            query['DriverIds'] = request.driver_ids
        if not DaraCore.is_null(request.driver_name):
            query['DriverName'] = request.driver_name
        if not DaraCore.is_null(request.filter):
            query['Filter'] = request.filter
        if not DaraCore.is_null(request.include_others):
            query['IncludeOthers'] = request.include_others
        if not DaraCore.is_null(request.lang):
            query['Lang'] = request.lang
        if not DaraCore.is_null(request.owner_type):
            query['OwnerType'] = request.owner_type
        if not DaraCore.is_null(request.page_number):
            query['PageNumber'] = request.page_number
        if not DaraCore.is_null(request.page_size):
            query['PageSize'] = request.page_size
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'DescribePeripheralDrivers',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.DescribePeripheralDriversResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def describe_peripheral_drivers(
        self,
        request: main_models.DescribePeripheralDriversRequest,
    ) -> main_models.DescribePeripheralDriversResponse:
        runtime = RuntimeOptions()
        return self.describe_peripheral_drivers_with_options(request, runtime)

    async def describe_peripheral_drivers_async(
        self,
        request: main_models.DescribePeripheralDriversRequest,
    ) -> main_models.DescribePeripheralDriversResponse:
        runtime = RuntimeOptions()
        return await self.describe_peripheral_drivers_with_options_async(request, runtime)

    def describe_user_cds_drives_with_options(
        self,
        request: main_models.DescribeUserCdsDrivesRequest,
        runtime: RuntimeOptions,
    ) -> main_models.DescribeUserCdsDrivesResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.ali_uid):
            query['AliUid'] = request.ali_uid
        if not DaraCore.is_null(request.directory_id):
            query['DirectoryId'] = request.directory_id
        if not DaraCore.is_null(request.directory_type):
            query['DirectoryType'] = request.directory_type
        if not DaraCore.is_null(request.end_user_id):
            query['EndUserId'] = request.end_user_id
        if not DaraCore.is_null(request.local_search):
            query['LocalSearch'] = request.local_search
        if not DaraCore.is_null(request.user_region_id):
            query['UserRegionId'] = request.user_region_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'DescribeUserCdsDrives',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.DescribeUserCdsDrivesResponse(),
            self.call_api(params, req, runtime)
        )

    async def describe_user_cds_drives_with_options_async(
        self,
        request: main_models.DescribeUserCdsDrivesRequest,
        runtime: RuntimeOptions,
    ) -> main_models.DescribeUserCdsDrivesResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.ali_uid):
            query['AliUid'] = request.ali_uid
        if not DaraCore.is_null(request.directory_id):
            query['DirectoryId'] = request.directory_id
        if not DaraCore.is_null(request.directory_type):
            query['DirectoryType'] = request.directory_type
        if not DaraCore.is_null(request.end_user_id):
            query['EndUserId'] = request.end_user_id
        if not DaraCore.is_null(request.local_search):
            query['LocalSearch'] = request.local_search
        if not DaraCore.is_null(request.user_region_id):
            query['UserRegionId'] = request.user_region_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'DescribeUserCdsDrives',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.DescribeUserCdsDrivesResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def describe_user_cds_drives(
        self,
        request: main_models.DescribeUserCdsDrivesRequest,
    ) -> main_models.DescribeUserCdsDrivesResponse:
        runtime = RuntimeOptions()
        return self.describe_user_cds_drives_with_options(request, runtime)

    async def describe_user_cds_drives_async(
        self,
        request: main_models.DescribeUserCdsDrivesRequest,
    ) -> main_models.DescribeUserCdsDrivesResponse:
        runtime = RuntimeOptions()
        return await self.describe_user_cds_drives_with_options_async(request, runtime)

    def describe_wy_drive_file_flat_with_options(
        self,
        request: main_models.DescribeWyDriveFileFlatRequest,
        runtime: RuntimeOptions,
    ) -> main_models.DescribeWyDriveFileFlatResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.max_results):
            query['MaxResults'] = request.max_results
        if not DaraCore.is_null(request.next_token):
            query['NextToken'] = request.next_token
        if not DaraCore.is_null(request.parent_folder):
            query['ParentFolder'] = request.parent_folder
        if not DaraCore.is_null(request.product_type):
            query['ProductType'] = request.product_type
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        if not DaraCore.is_null(request.wy_drive_owner_id):
            query['WyDriveOwnerId'] = request.wy_drive_owner_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'DescribeWyDriveFileFlat',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.DescribeWyDriveFileFlatResponse(),
            self.call_api(params, req, runtime)
        )

    async def describe_wy_drive_file_flat_with_options_async(
        self,
        request: main_models.DescribeWyDriveFileFlatRequest,
        runtime: RuntimeOptions,
    ) -> main_models.DescribeWyDriveFileFlatResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.max_results):
            query['MaxResults'] = request.max_results
        if not DaraCore.is_null(request.next_token):
            query['NextToken'] = request.next_token
        if not DaraCore.is_null(request.parent_folder):
            query['ParentFolder'] = request.parent_folder
        if not DaraCore.is_null(request.product_type):
            query['ProductType'] = request.product_type
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        if not DaraCore.is_null(request.wy_drive_owner_id):
            query['WyDriveOwnerId'] = request.wy_drive_owner_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'DescribeWyDriveFileFlat',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.DescribeWyDriveFileFlatResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def describe_wy_drive_file_flat(
        self,
        request: main_models.DescribeWyDriveFileFlatRequest,
    ) -> main_models.DescribeWyDriveFileFlatResponse:
        runtime = RuntimeOptions()
        return self.describe_wy_drive_file_flat_with_options(request, runtime)

    async def describe_wy_drive_file_flat_async(
        self,
        request: main_models.DescribeWyDriveFileFlatRequest,
    ) -> main_models.DescribeWyDriveFileFlatResponse:
        runtime = RuntimeOptions()
        return await self.describe_wy_drive_file_flat_with_options_async(request, runtime)

    def fetch_cloud_disk_meta_info_with_options(
        self,
        request: main_models.FetchCloudDiskMetaInfoRequest,
        runtime: RuntimeOptions,
    ) -> main_models.FetchCloudDiskMetaInfoResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.cds_ids):
            query['CdsIds'] = request.cds_ids
        if not DaraCore.is_null(request.user_region_id):
            query['UserRegionId'] = request.user_region_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'FetchCloudDiskMetaInfo',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.FetchCloudDiskMetaInfoResponse(),
            self.call_api(params, req, runtime)
        )

    async def fetch_cloud_disk_meta_info_with_options_async(
        self,
        request: main_models.FetchCloudDiskMetaInfoRequest,
        runtime: RuntimeOptions,
    ) -> main_models.FetchCloudDiskMetaInfoResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.cds_ids):
            query['CdsIds'] = request.cds_ids
        if not DaraCore.is_null(request.user_region_id):
            query['UserRegionId'] = request.user_region_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'FetchCloudDiskMetaInfo',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.FetchCloudDiskMetaInfoResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def fetch_cloud_disk_meta_info(
        self,
        request: main_models.FetchCloudDiskMetaInfoRequest,
    ) -> main_models.FetchCloudDiskMetaInfoResponse:
        runtime = RuntimeOptions()
        return self.fetch_cloud_disk_meta_info_with_options(request, runtime)

    async def fetch_cloud_disk_meta_info_async(
        self,
        request: main_models.FetchCloudDiskMetaInfoRequest,
    ) -> main_models.FetchCloudDiskMetaInfoResponse:
        runtime = RuntimeOptions()
        return await self.fetch_cloud_disk_meta_info_with_options_async(request, runtime)

    def get_download_url_with_options(
        self,
        request: main_models.GetDownloadUrlRequest,
        runtime: RuntimeOptions,
    ) -> main_models.GetDownloadUrlResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.file_id):
            query['FileId'] = request.file_id
        if not DaraCore.is_null(request.product_type):
            query['ProductType'] = request.product_type
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        if not DaraCore.is_null(request.wy_drive_owner_id):
            query['WyDriveOwnerId'] = request.wy_drive_owner_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'GetDownloadUrl',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.GetDownloadUrlResponse(),
            self.call_api(params, req, runtime)
        )

    async def get_download_url_with_options_async(
        self,
        request: main_models.GetDownloadUrlRequest,
        runtime: RuntimeOptions,
    ) -> main_models.GetDownloadUrlResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.file_id):
            query['FileId'] = request.file_id
        if not DaraCore.is_null(request.product_type):
            query['ProductType'] = request.product_type
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        if not DaraCore.is_null(request.wy_drive_owner_id):
            query['WyDriveOwnerId'] = request.wy_drive_owner_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'GetDownloadUrl',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.GetDownloadUrlResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def get_download_url(
        self,
        request: main_models.GetDownloadUrlRequest,
    ) -> main_models.GetDownloadUrlResponse:
        runtime = RuntimeOptions()
        return self.get_download_url_with_options(request, runtime)

    async def get_download_url_async(
        self,
        request: main_models.GetDownloadUrlRequest,
    ) -> main_models.GetDownloadUrlResponse:
        runtime = RuntimeOptions()
        return await self.get_download_url_with_options_async(request, runtime)

    def get_download_url_by_path_with_options(
        self,
        request: main_models.GetDownloadUrlByPathRequest,
        runtime: RuntimeOptions,
    ) -> main_models.GetDownloadUrlByPathResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.file_path):
            query['FilePath'] = request.file_path
        if not DaraCore.is_null(request.product_type):
            query['ProductType'] = request.product_type
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        if not DaraCore.is_null(request.wy_drive_owner_id):
            query['WyDriveOwnerId'] = request.wy_drive_owner_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'GetDownloadUrlByPath',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.GetDownloadUrlByPathResponse(),
            self.call_api(params, req, runtime)
        )

    async def get_download_url_by_path_with_options_async(
        self,
        request: main_models.GetDownloadUrlByPathRequest,
        runtime: RuntimeOptions,
    ) -> main_models.GetDownloadUrlByPathResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.file_path):
            query['FilePath'] = request.file_path
        if not DaraCore.is_null(request.product_type):
            query['ProductType'] = request.product_type
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        if not DaraCore.is_null(request.wy_drive_owner_id):
            query['WyDriveOwnerId'] = request.wy_drive_owner_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'GetDownloadUrlByPath',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.GetDownloadUrlByPathResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def get_download_url_by_path(
        self,
        request: main_models.GetDownloadUrlByPathRequest,
    ) -> main_models.GetDownloadUrlByPathResponse:
        runtime = RuntimeOptions()
        return self.get_download_url_by_path_with_options(request, runtime)

    async def get_download_url_by_path_async(
        self,
        request: main_models.GetDownloadUrlByPathRequest,
    ) -> main_models.GetDownloadUrlByPathResponse:
        runtime = RuntimeOptions()
        return await self.get_download_url_by_path_with_options_async(request, runtime)

    def get_global_token_with_options(
        self,
        request: main_models.GetGlobalTokenRequest,
        runtime: RuntimeOptions,
    ) -> main_models.GetGlobalTokenResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.directory_id):
            query['DirectoryId'] = request.directory_id
        if not DaraCore.is_null(request.directory_type):
            query['DirectoryType'] = request.directory_type
        if not DaraCore.is_null(request.domain_name):
            query['DomainName'] = request.domain_name
        if not DaraCore.is_null(request.end_user_id):
            query['EndUserId'] = request.end_user_id
        if not DaraCore.is_null(request.sid):
            query['Sid'] = request.sid
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'GetGlobalToken',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.GetGlobalTokenResponse(),
            self.call_api(params, req, runtime)
        )

    async def get_global_token_with_options_async(
        self,
        request: main_models.GetGlobalTokenRequest,
        runtime: RuntimeOptions,
    ) -> main_models.GetGlobalTokenResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.directory_id):
            query['DirectoryId'] = request.directory_id
        if not DaraCore.is_null(request.directory_type):
            query['DirectoryType'] = request.directory_type
        if not DaraCore.is_null(request.domain_name):
            query['DomainName'] = request.domain_name
        if not DaraCore.is_null(request.end_user_id):
            query['EndUserId'] = request.end_user_id
        if not DaraCore.is_null(request.sid):
            query['Sid'] = request.sid
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'GetGlobalToken',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.GetGlobalTokenResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def get_global_token(
        self,
        request: main_models.GetGlobalTokenRequest,
    ) -> main_models.GetGlobalTokenResponse:
        runtime = RuntimeOptions()
        return self.get_global_token_with_options(request, runtime)

    async def get_global_token_async(
        self,
        request: main_models.GetGlobalTokenRequest,
    ) -> main_models.GetGlobalTokenResponse:
        runtime = RuntimeOptions()
        return await self.get_global_token_with_options_async(request, runtime)

    def get_token_with_options(
        self,
        request: main_models.GetTokenRequest,
        runtime: RuntimeOptions,
    ) -> main_models.GetTokenResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.cds_id):
            query['CdsId'] = request.cds_id
        if not DaraCore.is_null(request.end_user_id):
            query['EndUserId'] = request.end_user_id
        if not DaraCore.is_null(request.region_id):
            query['RegionId'] = request.region_id
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        if not DaraCore.is_null(request.user_region_id):
            query['UserRegionId'] = request.user_region_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'GetToken',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.GetTokenResponse(),
            self.call_api(params, req, runtime)
        )

    async def get_token_with_options_async(
        self,
        request: main_models.GetTokenRequest,
        runtime: RuntimeOptions,
    ) -> main_models.GetTokenResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.cds_id):
            query['CdsId'] = request.cds_id
        if not DaraCore.is_null(request.end_user_id):
            query['EndUserId'] = request.end_user_id
        if not DaraCore.is_null(request.region_id):
            query['RegionId'] = request.region_id
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        if not DaraCore.is_null(request.user_region_id):
            query['UserRegionId'] = request.user_region_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'GetToken',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.GetTokenResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def get_token(
        self,
        request: main_models.GetTokenRequest,
    ) -> main_models.GetTokenResponse:
        runtime = RuntimeOptions()
        return self.get_token_with_options(request, runtime)

    async def get_token_async(
        self,
        request: main_models.GetTokenRequest,
    ) -> main_models.GetTokenResponse:
        runtime = RuntimeOptions()
        return await self.get_token_with_options_async(request, runtime)

    def get_upload_url_with_options(
        self,
        request: main_models.GetUploadUrlRequest,
        runtime: RuntimeOptions,
    ) -> main_models.GetUploadUrlResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.file_path):
            query['FilePath'] = request.file_path
        if not DaraCore.is_null(request.product_type):
            query['ProductType'] = request.product_type
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        if not DaraCore.is_null(request.wy_drive_owner_id):
            query['WyDriveOwnerId'] = request.wy_drive_owner_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'GetUploadUrl',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.GetUploadUrlResponse(),
            self.call_api(params, req, runtime)
        )

    async def get_upload_url_with_options_async(
        self,
        request: main_models.GetUploadUrlRequest,
        runtime: RuntimeOptions,
    ) -> main_models.GetUploadUrlResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.file_path):
            query['FilePath'] = request.file_path
        if not DaraCore.is_null(request.product_type):
            query['ProductType'] = request.product_type
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        if not DaraCore.is_null(request.wy_drive_owner_id):
            query['WyDriveOwnerId'] = request.wy_drive_owner_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'GetUploadUrl',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.GetUploadUrlResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def get_upload_url(
        self,
        request: main_models.GetUploadUrlRequest,
    ) -> main_models.GetUploadUrlResponse:
        runtime = RuntimeOptions()
        return self.get_upload_url_with_options(request, runtime)

    async def get_upload_url_async(
        self,
        request: main_models.GetUploadUrlRequest,
    ) -> main_models.GetUploadUrlResponse:
        runtime = RuntimeOptions()
        return await self.get_upload_url_with_options_async(request, runtime)

    def list_gray_levels_with_options(
        self,
        request: main_models.ListGrayLevelsRequest,
        runtime: RuntimeOptions,
    ) -> main_models.ListGrayLevelsResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'ListGrayLevels',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.ListGrayLevelsResponse(),
            self.call_api(params, req, runtime)
        )

    async def list_gray_levels_with_options_async(
        self,
        request: main_models.ListGrayLevelsRequest,
        runtime: RuntimeOptions,
    ) -> main_models.ListGrayLevelsResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'ListGrayLevels',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.ListGrayLevelsResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def list_gray_levels(
        self,
        request: main_models.ListGrayLevelsRequest,
    ) -> main_models.ListGrayLevelsResponse:
        runtime = RuntimeOptions()
        return self.list_gray_levels_with_options(request, runtime)

    async def list_gray_levels_async(
        self,
        request: main_models.ListGrayLevelsRequest,
    ) -> main_models.ListGrayLevelsResponse:
        runtime = RuntimeOptions()
        return await self.list_gray_levels_with_options_async(request, runtime)

    def modify_gray_versions_with_options(
        self,
        request: main_models.ModifyGrayVersionsRequest,
        runtime: RuntimeOptions,
    ) -> main_models.ModifyGrayVersionsResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        if not DaraCore.is_null(request.versions):
            query['Versions'] = request.versions
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'ModifyGrayVersions',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.ModifyGrayVersionsResponse(),
            self.call_api(params, req, runtime)
        )

    async def modify_gray_versions_with_options_async(
        self,
        request: main_models.ModifyGrayVersionsRequest,
        runtime: RuntimeOptions,
    ) -> main_models.ModifyGrayVersionsResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        if not DaraCore.is_null(request.versions):
            query['Versions'] = request.versions
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'ModifyGrayVersions',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.ModifyGrayVersionsResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def modify_gray_versions(
        self,
        request: main_models.ModifyGrayVersionsRequest,
    ) -> main_models.ModifyGrayVersionsResponse:
        runtime = RuntimeOptions()
        return self.modify_gray_versions_with_options(request, runtime)

    async def modify_gray_versions_async(
        self,
        request: main_models.ModifyGrayVersionsRequest,
    ) -> main_models.ModifyGrayVersionsResponse:
        runtime = RuntimeOptions()
        return await self.modify_gray_versions_with_options_async(request, runtime)

    def pre_upload_file_with_options(
        self,
        request: main_models.PreUploadFileRequest,
        runtime: RuntimeOptions,
    ) -> main_models.PreUploadFileResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.file_id):
            query['FileId'] = request.file_id
        if not DaraCore.is_null(request.file_name):
            query['FileName'] = request.file_name
        if not DaraCore.is_null(request.parent_folder_id):
            query['ParentFolderId'] = request.parent_folder_id
        if not DaraCore.is_null(request.parent_folder_path):
            query['ParentFolderPath'] = request.parent_folder_path
        if not DaraCore.is_null(request.product_type):
            query['ProductType'] = request.product_type
        if not DaraCore.is_null(request.tags):
            query['Tags'] = request.tags
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        if not DaraCore.is_null(request.wy_drive_owner_id):
            query['WyDriveOwnerId'] = request.wy_drive_owner_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'PreUploadFile',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.PreUploadFileResponse(),
            self.call_api(params, req, runtime)
        )

    async def pre_upload_file_with_options_async(
        self,
        request: main_models.PreUploadFileRequest,
        runtime: RuntimeOptions,
    ) -> main_models.PreUploadFileResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.file_id):
            query['FileId'] = request.file_id
        if not DaraCore.is_null(request.file_name):
            query['FileName'] = request.file_name
        if not DaraCore.is_null(request.parent_folder_id):
            query['ParentFolderId'] = request.parent_folder_id
        if not DaraCore.is_null(request.parent_folder_path):
            query['ParentFolderPath'] = request.parent_folder_path
        if not DaraCore.is_null(request.product_type):
            query['ProductType'] = request.product_type
        if not DaraCore.is_null(request.tags):
            query['Tags'] = request.tags
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        if not DaraCore.is_null(request.wy_drive_owner_id):
            query['WyDriveOwnerId'] = request.wy_drive_owner_id
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'PreUploadFile',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.PreUploadFileResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def pre_upload_file(
        self,
        request: main_models.PreUploadFileRequest,
    ) -> main_models.PreUploadFileResponse:
        runtime = RuntimeOptions()
        return self.pre_upload_file_with_options(request, runtime)

    async def pre_upload_file_async(
        self,
        request: main_models.PreUploadFileRequest,
    ) -> main_models.PreUploadFileResponse:
        runtime = RuntimeOptions()
        return await self.pre_upload_file_with_options_async(request, runtime)

    def remove_gray_versions_with_options(
        self,
        request: main_models.RemoveGrayVersionsRequest,
        runtime: RuntimeOptions,
    ) -> main_models.RemoveGrayVersionsResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        if not DaraCore.is_null(request.versions):
            query['Versions'] = request.versions
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'RemoveGrayVersions',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.RemoveGrayVersionsResponse(),
            self.call_api(params, req, runtime)
        )

    async def remove_gray_versions_with_options_async(
        self,
        request: main_models.RemoveGrayVersionsRequest,
        runtime: RuntimeOptions,
    ) -> main_models.RemoveGrayVersionsResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        if not DaraCore.is_null(request.versions):
            query['Versions'] = request.versions
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'RemoveGrayVersions',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.RemoveGrayVersionsResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def remove_gray_versions(
        self,
        request: main_models.RemoveGrayVersionsRequest,
    ) -> main_models.RemoveGrayVersionsResponse:
        runtime = RuntimeOptions()
        return self.remove_gray_versions_with_options(request, runtime)

    async def remove_gray_versions_async(
        self,
        request: main_models.RemoveGrayVersionsRequest,
    ) -> main_models.RemoveGrayVersionsResponse:
        runtime = RuntimeOptions()
        return await self.remove_gray_versions_with_options_async(request, runtime)

    def remove_peripheral_drivers_with_options(
        self,
        request: main_models.RemovePeripheralDriversRequest,
        runtime: RuntimeOptions,
    ) -> main_models.RemovePeripheralDriversResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.driver_ids):
            query['DriverIds'] = request.driver_ids
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'RemovePeripheralDrivers',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.RemovePeripheralDriversResponse(),
            self.call_api(params, req, runtime)
        )

    async def remove_peripheral_drivers_with_options_async(
        self,
        request: main_models.RemovePeripheralDriversRequest,
        runtime: RuntimeOptions,
    ) -> main_models.RemovePeripheralDriversResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.driver_ids):
            query['DriverIds'] = request.driver_ids
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'RemovePeripheralDrivers',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.RemovePeripheralDriversResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def remove_peripheral_drivers(
        self,
        request: main_models.RemovePeripheralDriversRequest,
    ) -> main_models.RemovePeripheralDriversResponse:
        runtime = RuntimeOptions()
        return self.remove_peripheral_drivers_with_options(request, runtime)

    async def remove_peripheral_drivers_async(
        self,
        request: main_models.RemovePeripheralDriversRequest,
    ) -> main_models.RemovePeripheralDriversResponse:
        runtime = RuntimeOptions()
        return await self.remove_peripheral_drivers_with_options_async(request, runtime)

    def turn_off_grayscale_whitelist_with_options(
        self,
        request: main_models.TurnOffGrayscaleWhitelistRequest,
        runtime: RuntimeOptions,
    ) -> main_models.TurnOffGrayscaleWhitelistResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'TurnOffGrayscaleWhitelist',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.TurnOffGrayscaleWhitelistResponse(),
            self.call_api(params, req, runtime)
        )

    async def turn_off_grayscale_whitelist_with_options_async(
        self,
        request: main_models.TurnOffGrayscaleWhitelistRequest,
        runtime: RuntimeOptions,
    ) -> main_models.TurnOffGrayscaleWhitelistResponse:
        request.validate()
        query = {}
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'TurnOffGrayscaleWhitelist',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.TurnOffGrayscaleWhitelistResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def turn_off_grayscale_whitelist(
        self,
        request: main_models.TurnOffGrayscaleWhitelistRequest,
    ) -> main_models.TurnOffGrayscaleWhitelistResponse:
        runtime = RuntimeOptions()
        return self.turn_off_grayscale_whitelist_with_options(request, runtime)

    async def turn_off_grayscale_whitelist_async(
        self,
        request: main_models.TurnOffGrayscaleWhitelistRequest,
    ) -> main_models.TurnOffGrayscaleWhitelistResponse:
        runtime = RuntimeOptions()
        return await self.turn_off_grayscale_whitelist_with_options_async(request, runtime)

    def turn_on_grayscale_whitelist_with_options(
        self,
        tmp_req: main_models.TurnOnGrayscaleWhitelistRequest,
        runtime: RuntimeOptions,
    ) -> main_models.TurnOnGrayscaleWhitelistResponse:
        tmp_req.validate()
        request = main_models.TurnOnGrayscaleWhitelistShrinkRequest()
        Utils.convert(tmp_req, request)
        if not DaraCore.is_null(tmp_req.append):
            request.append_shrink = Utils.array_to_string_with_specified_style(tmp_req.append, 'Append', 'json')
        query = {}
        if not DaraCore.is_null(request.append_shrink):
            query['Append'] = request.append_shrink
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'TurnOnGrayscaleWhitelist',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.TurnOnGrayscaleWhitelistResponse(),
            self.call_api(params, req, runtime)
        )

    async def turn_on_grayscale_whitelist_with_options_async(
        self,
        tmp_req: main_models.TurnOnGrayscaleWhitelistRequest,
        runtime: RuntimeOptions,
    ) -> main_models.TurnOnGrayscaleWhitelistResponse:
        tmp_req.validate()
        request = main_models.TurnOnGrayscaleWhitelistShrinkRequest()
        Utils.convert(tmp_req, request)
        if not DaraCore.is_null(tmp_req.append):
            request.append_shrink = Utils.array_to_string_with_specified_style(tmp_req.append, 'Append', 'json')
        query = {}
        if not DaraCore.is_null(request.append_shrink):
            query['Append'] = request.append_shrink
        if not DaraCore.is_null(request.user_ali_uid):
            query['UserAliUid'] = request.user_ali_uid
        req = open_api_util_models.OpenApiRequest(
            query = Utils.query(query)
        )
        params = open_api_util_models.Params(
            action = 'TurnOnGrayscaleWhitelist',
            version = '2023-01-17',
            protocol = 'HTTPS',
            pathname = '/',
            method = 'POST',
            auth_type = 'AK',
            style = 'RPC',
            req_body_type = 'formData',
            body_type = 'json'
        )
        return DaraCore.from_map(
            main_models.TurnOnGrayscaleWhitelistResponse(),
            await self.call_api_async(params, req, runtime)
        )

    def turn_on_grayscale_whitelist(
        self,
        request: main_models.TurnOnGrayscaleWhitelistRequest,
    ) -> main_models.TurnOnGrayscaleWhitelistResponse:
        runtime = RuntimeOptions()
        return self.turn_on_grayscale_whitelist_with_options(request, runtime)

    async def turn_on_grayscale_whitelist_async(
        self,
        request: main_models.TurnOnGrayscaleWhitelistRequest,
    ) -> main_models.TurnOnGrayscaleWhitelistResponse:
        runtime = RuntimeOptions()
        return await self.turn_on_grayscale_whitelist_with_options_async(request, runtime)
