# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import <PERSON><PERSON><PERSON><PERSON><PERSON> 
from typing import List


class DescribeFileTransferApprovalConfigsShrinkRequest(DaraModel):
    def __init__(
        self,
        account_type: str = None,
        ad_domain: str = None,
        scene: str = None,
        task_type: str = None,
        user_ali_uid: int = None,
        user_groups: List[str] = None,
        user_infos_shrink: str = None,
    ):
        self.account_type = account_type
        self.ad_domain = ad_domain
        self.scene = scene
        self.task_type = task_type
        self.user_ali_uid = user_ali_uid
        self.user_groups = user_groups
        self.user_infos_shrink = user_infos_shrink

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.account_type is not None:
            result['AccountType'] = self.account_type

        if self.ad_domain is not None:
            result['AdDomain'] = self.ad_domain

        if self.scene is not None:
            result['Scene'] = self.scene

        if self.task_type is not None:
            result['TaskType'] = self.task_type

        if self.user_ali_uid is not None:
            result['UserAliUid'] = self.user_ali_uid

        if self.user_groups is not None:
            result['UserGroups'] = self.user_groups

        if self.user_infos_shrink is not None:
            result['UserInfos'] = self.user_infos_shrink

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AccountType') is not None:
            self.account_type = m.get('AccountType')

        if m.get('AdDomain') is not None:
            self.ad_domain = m.get('AdDomain')

        if m.get('Scene') is not None:
            self.scene = m.get('Scene')

        if m.get('TaskType') is not None:
            self.task_type = m.get('TaskType')

        if m.get('UserAliUid') is not None:
            self.user_ali_uid = m.get('UserAliUid')

        if m.get('UserGroups') is not None:
            self.user_groups = m.get('UserGroups')

        if m.get('UserInfos') is not None:
            self.user_infos_shrink = m.get('UserInfos')

        return self

