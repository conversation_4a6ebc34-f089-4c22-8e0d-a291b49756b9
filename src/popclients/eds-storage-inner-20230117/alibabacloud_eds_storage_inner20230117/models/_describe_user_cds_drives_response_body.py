# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_eds_storage_inner20230117 import models as main_models 
from typing import List


class DescribeUserCdsDrivesResponseBody(DaraModel):
    def __init__(
        self,
        cds_drive_models: List[main_models.DescribeUserCdsDrivesResponseBodyCdsDriveModels] = None,
        request_id: str = None,
    ):
        self.cds_drive_models = cds_drive_models
        self.request_id = request_id

    def validate(self):
        if self.cds_drive_models:
            for v1 in self.cds_drive_models:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        result['CdsDriveModels'] = []
        if self.cds_drive_models is not None:
            for k1 in self.cds_drive_models:
                result['CdsDriveModels'].append(k1.to_map() if k1 else None)

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        self.cds_drive_models = []
        if m.get('CdsDriveModels') is not None:
            for k1 in m.get('CdsDriveModels'):
                temp_model = main_models.DescribeUserCdsDrivesResponseBodyCdsDriveModels()
                self.cds_drive_models.append(temp_model.from_map(k1))

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        return self

class DescribeUserCdsDrivesResponseBodyCdsDriveModels(DaraModel):
    def __init__(
        self,
        ad_office_sites: List[main_models.DescribeUserCdsDrivesResponseBodyCdsDriveModelsAdOfficeSites] = None,
        allow_client_display: str = None,
        biz_type: int = None,
        cds_charge_status: str = None,
        cds_charge_type: str = None,
        cds_id: str = None,
        cds_name: str = None,
        cds_owner_type: int = None,
        cen_id: str = None,
        directory_type: str = None,
        domain_id: str = None,
        domain_name: str = None,
        drive_id: str = None,
        drive_type: str = None,
        external_drive_id: str = None,
        global_status: int = None,
        instance_id: str = None,
        order_id: str = None,
        pds_subdomain_id: str = None,
        region_id: str = None,
        sid: str = None,
        solution_id: str = None,
        status: int = None,
        user_allow_client_display: str = None,
        user_count_quota: int = None,
        max_size: int = None,
    ):
        self.ad_office_sites = ad_office_sites
        self.allow_client_display = allow_client_display
        self.biz_type = biz_type
        self.cds_charge_status = cds_charge_status
        self.cds_charge_type = cds_charge_type
        self.cds_id = cds_id
        self.cds_name = cds_name
        self.cds_owner_type = cds_owner_type
        self.cen_id = cen_id
        self.directory_type = directory_type
        self.domain_id = domain_id
        self.domain_name = domain_name
        self.drive_id = drive_id
        self.drive_type = drive_type
        self.external_drive_id = external_drive_id
        self.global_status = global_status
        self.instance_id = instance_id
        self.order_id = order_id
        self.pds_subdomain_id = pds_subdomain_id
        self.region_id = region_id
        self.sid = sid
        self.solution_id = solution_id
        self.status = status
        self.user_allow_client_display = user_allow_client_display
        self.user_count_quota = user_count_quota
        self.max_size = max_size

    def validate(self):
        if self.ad_office_sites:
            for v1 in self.ad_office_sites:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        result['AdOfficeSites'] = []
        if self.ad_office_sites is not None:
            for k1 in self.ad_office_sites:
                result['AdOfficeSites'].append(k1.to_map() if k1 else None)

        if self.allow_client_display is not None:
            result['AllowClientDisplay'] = self.allow_client_display

        if self.biz_type is not None:
            result['BizType'] = self.biz_type

        if self.cds_charge_status is not None:
            result['CdsChargeStatus'] = self.cds_charge_status

        if self.cds_charge_type is not None:
            result['CdsChargeType'] = self.cds_charge_type

        if self.cds_id is not None:
            result['CdsId'] = self.cds_id

        if self.cds_name is not None:
            result['CdsName'] = self.cds_name

        if self.cds_owner_type is not None:
            result['CdsOwnerType'] = self.cds_owner_type

        if self.cen_id is not None:
            result['CenId'] = self.cen_id

        if self.directory_type is not None:
            result['DirectoryType'] = self.directory_type

        if self.domain_id is not None:
            result['DomainId'] = self.domain_id

        if self.domain_name is not None:
            result['DomainName'] = self.domain_name

        if self.drive_id is not None:
            result['DriveId'] = self.drive_id

        if self.drive_type is not None:
            result['DriveType'] = self.drive_type

        if self.external_drive_id is not None:
            result['ExternalDriveId'] = self.external_drive_id

        if self.global_status is not None:
            result['GlobalStatus'] = self.global_status

        if self.instance_id is not None:
            result['InstanceId'] = self.instance_id

        if self.order_id is not None:
            result['OrderId'] = self.order_id

        if self.pds_subdomain_id is not None:
            result['PdsSubdomainId'] = self.pds_subdomain_id

        if self.region_id is not None:
            result['RegionId'] = self.region_id

        if self.sid is not None:
            result['Sid'] = self.sid

        if self.solution_id is not None:
            result['SolutionId'] = self.solution_id

        if self.status is not None:
            result['Status'] = self.status

        if self.user_allow_client_display is not None:
            result['UserAllowClientDisplay'] = self.user_allow_client_display

        if self.user_count_quota is not None:
            result['UserCountQuota'] = self.user_count_quota

        if self.max_size is not None:
            result['maxSize'] = self.max_size

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        self.ad_office_sites = []
        if m.get('AdOfficeSites') is not None:
            for k1 in m.get('AdOfficeSites'):
                temp_model = main_models.DescribeUserCdsDrivesResponseBodyCdsDriveModelsAdOfficeSites()
                self.ad_office_sites.append(temp_model.from_map(k1))

        if m.get('AllowClientDisplay') is not None:
            self.allow_client_display = m.get('AllowClientDisplay')

        if m.get('BizType') is not None:
            self.biz_type = m.get('BizType')

        if m.get('CdsChargeStatus') is not None:
            self.cds_charge_status = m.get('CdsChargeStatus')

        if m.get('CdsChargeType') is not None:
            self.cds_charge_type = m.get('CdsChargeType')

        if m.get('CdsId') is not None:
            self.cds_id = m.get('CdsId')

        if m.get('CdsName') is not None:
            self.cds_name = m.get('CdsName')

        if m.get('CdsOwnerType') is not None:
            self.cds_owner_type = m.get('CdsOwnerType')

        if m.get('CenId') is not None:
            self.cen_id = m.get('CenId')

        if m.get('DirectoryType') is not None:
            self.directory_type = m.get('DirectoryType')

        if m.get('DomainId') is not None:
            self.domain_id = m.get('DomainId')

        if m.get('DomainName') is not None:
            self.domain_name = m.get('DomainName')

        if m.get('DriveId') is not None:
            self.drive_id = m.get('DriveId')

        if m.get('DriveType') is not None:
            self.drive_type = m.get('DriveType')

        if m.get('ExternalDriveId') is not None:
            self.external_drive_id = m.get('ExternalDriveId')

        if m.get('GlobalStatus') is not None:
            self.global_status = m.get('GlobalStatus')

        if m.get('InstanceId') is not None:
            self.instance_id = m.get('InstanceId')

        if m.get('OrderId') is not None:
            self.order_id = m.get('OrderId')

        if m.get('PdsSubdomainId') is not None:
            self.pds_subdomain_id = m.get('PdsSubdomainId')

        if m.get('RegionId') is not None:
            self.region_id = m.get('RegionId')

        if m.get('Sid') is not None:
            self.sid = m.get('Sid')

        if m.get('SolutionId') is not None:
            self.solution_id = m.get('SolutionId')

        if m.get('Status') is not None:
            self.status = m.get('Status')

        if m.get('UserAllowClientDisplay') is not None:
            self.user_allow_client_display = m.get('UserAllowClientDisplay')

        if m.get('UserCountQuota') is not None:
            self.user_count_quota = m.get('UserCountQuota')

        if m.get('maxSize') is not None:
            self.max_size = m.get('maxSize')

        return self

class DescribeUserCdsDrivesResponseBodyCdsDriveModelsAdOfficeSites(DaraModel):
    def __init__(
        self,
        office_site_id: str = None,
        office_site_name: str = None,
    ):
        self.office_site_id = office_site_id
        self.office_site_name = office_site_name

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.office_site_id is not None:
            result['OfficeSiteId'] = self.office_site_id

        if self.office_site_name is not None:
            result['OfficeSiteName'] = self.office_site_name

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('OfficeSiteId') is not None:
            self.office_site_id = m.get('OfficeSiteId')

        if m.get('OfficeSiteName') is not None:
            self.office_site_name = m.get('OfficeSiteName')

        return self

