# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribePeripheralDriverCategoryRequest(DaraModel):
    def __init__(
        self,
        device_type: str = None,
        owner_type: str = None,
        user_ali_uid: int = None,
    ):
        self.device_type = device_type
        self.owner_type = owner_type
        self.user_ali_uid = user_ali_uid

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.device_type is not None:
            result['DeviceType'] = self.device_type

        if self.owner_type is not None:
            result['OwnerType'] = self.owner_type

        if self.user_ali_uid is not None:
            result['UserAliUid'] = self.user_ali_uid

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('DeviceType') is not None:
            self.device_type = m.get('DeviceType')

        if m.get('OwnerType') is not None:
            self.owner_type = m.get('OwnerType')

        if m.get('UserAliUid') is not None:
            self.user_ali_uid = m.get('UserAliUid')

        return self

