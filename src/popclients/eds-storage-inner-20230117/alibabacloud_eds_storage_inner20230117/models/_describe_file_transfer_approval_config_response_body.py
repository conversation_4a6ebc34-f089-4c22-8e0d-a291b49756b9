# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribeFileTransferApprovalConfigResponseBody(DaraModel):
    def __init__(
        self,
        approver_email: str = None,
        approver_name: str = None,
        approver_phone: str = None,
        need_approval: bool = None,
        request_id: str = None,
    ):
        self.approver_email = approver_email
        self.approver_name = approver_name
        self.approver_phone = approver_phone
        self.need_approval = need_approval
        self.request_id = request_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.approver_email is not None:
            result['ApproverEmail'] = self.approver_email

        if self.approver_name is not None:
            result['ApproverName'] = self.approver_name

        if self.approver_phone is not None:
            result['ApproverPhone'] = self.approver_phone

        if self.need_approval is not None:
            result['NeedApproval'] = self.need_approval

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('ApproverEmail') is not None:
            self.approver_email = m.get('ApproverEmail')

        if m.get('ApproverName') is not None:
            self.approver_name = m.get('ApproverName')

        if m.get('ApproverPhone') is not None:
            self.approver_phone = m.get('ApproverPhone')

        if m.get('NeedApproval') is not None:
            self.need_approval = m.get('NeedApproval')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        return self

