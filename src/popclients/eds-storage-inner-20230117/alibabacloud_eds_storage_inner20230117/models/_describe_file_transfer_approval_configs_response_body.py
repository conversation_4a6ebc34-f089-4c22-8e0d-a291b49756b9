# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_eds_storage_inner20230117 import models as main_models 
from typing import List


class DescribeFileTransferApprovalConfigsResponseBody(DaraModel):
    def __init__(
        self,
        request_id: str = None,
        results: List[main_models.DescribeFileTransferApprovalConfigsResponseBodyResults] = None,
    ):
        self.request_id = request_id
        self.results = results

    def validate(self):
        if self.results:
            for v1 in self.results:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.request_id is not None:
            result['RequestId'] = self.request_id

        result['Results'] = []
        if self.results is not None:
            for k1 in self.results:
                result['Results'].append(k1.to_map() if k1 else None)

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        self.results = []
        if m.get('Results') is not None:
            for k1 in m.get('Results'):
                temp_model = main_models.DescribeFileTransferApprovalConfigsResponseBodyResults()
                self.results.append(temp_model.from_map(k1))

        return self

class DescribeFileTransferApprovalConfigsResponseBodyResults(DaraModel):
    def __init__(
        self,
        need_approval: bool = None,
        target_id: str = None,
    ):
        self.need_approval = need_approval
        self.target_id = target_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.need_approval is not None:
            result['NeedApproval'] = self.need_approval

        if self.target_id is not None:
            result['TargetId'] = self.target_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('NeedApproval') is not None:
            self.need_approval = m.get('NeedApproval')

        if m.get('TargetId') is not None:
            self.target_id = m.get('TargetId')

        return self

