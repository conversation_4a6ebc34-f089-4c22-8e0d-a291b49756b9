# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribeGlobalCloudDriveServicesRequest(DaraModel):
    def __init__(
        self,
        ali_uid: int = None,
        biz_type: int = None,
        cds_id: str = None,
        directory_type: str = None,
        domain_name: str = None,
        global_status: str = None,
        is_token_needed: bool = None,
        max_results: int = None,
        next_token: str = None,
        pds_subdomain_id: str = None,
        sid: str = None,
        solution_id: str = None,
        user_region_id: str = None,
    ):
        self.ali_uid = ali_uid
        self.biz_type = biz_type
        self.cds_id = cds_id
        self.directory_type = directory_type
        self.domain_name = domain_name
        self.global_status = global_status
        self.is_token_needed = is_token_needed
        self.max_results = max_results
        self.next_token = next_token
        self.pds_subdomain_id = pds_subdomain_id
        self.sid = sid
        self.solution_id = solution_id
        self.user_region_id = user_region_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.ali_uid is not None:
            result['AliUid'] = self.ali_uid

        if self.biz_type is not None:
            result['BizType'] = self.biz_type

        if self.cds_id is not None:
            result['CdsId'] = self.cds_id

        if self.directory_type is not None:
            result['DirectoryType'] = self.directory_type

        if self.domain_name is not None:
            result['DomainName'] = self.domain_name

        if self.global_status is not None:
            result['GlobalStatus'] = self.global_status

        if self.is_token_needed is not None:
            result['IsTokenNeeded'] = self.is_token_needed

        if self.max_results is not None:
            result['MaxResults'] = self.max_results

        if self.next_token is not None:
            result['NextToken'] = self.next_token

        if self.pds_subdomain_id is not None:
            result['PdsSubdomainId'] = self.pds_subdomain_id

        if self.sid is not None:
            result['Sid'] = self.sid

        if self.solution_id is not None:
            result['SolutionId'] = self.solution_id

        if self.user_region_id is not None:
            result['UserRegionId'] = self.user_region_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AliUid') is not None:
            self.ali_uid = m.get('AliUid')

        if m.get('BizType') is not None:
            self.biz_type = m.get('BizType')

        if m.get('CdsId') is not None:
            self.cds_id = m.get('CdsId')

        if m.get('DirectoryType') is not None:
            self.directory_type = m.get('DirectoryType')

        if m.get('DomainName') is not None:
            self.domain_name = m.get('DomainName')

        if m.get('GlobalStatus') is not None:
            self.global_status = m.get('GlobalStatus')

        if m.get('IsTokenNeeded') is not None:
            self.is_token_needed = m.get('IsTokenNeeded')

        if m.get('MaxResults') is not None:
            self.max_results = m.get('MaxResults')

        if m.get('NextToken') is not None:
            self.next_token = m.get('NextToken')

        if m.get('PdsSubdomainId') is not None:
            self.pds_subdomain_id = m.get('PdsSubdomainId')

        if m.get('Sid') is not None:
            self.sid = m.get('Sid')

        if m.get('SolutionId') is not None:
            self.solution_id = m.get('SolutionId')

        if m.get('UserRegionId') is not None:
            self.user_region_id = m.get('UserRegionId')

        return self

