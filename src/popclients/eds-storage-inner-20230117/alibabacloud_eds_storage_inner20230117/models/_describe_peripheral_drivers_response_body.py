# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_eds_storage_inner20230117 import models as main_models 
from typing import List


class DescribePeripheralDriversResponseBody(DaraModel):
    def __init__(
        self,
        count: int = None,
        drivers: List[main_models.DescribePeripheralDriversResponseBodyDrivers] = None,
        request_id: str = None,
    ):
        self.count = count
        self.drivers = drivers
        self.request_id = request_id

    def validate(self):
        if self.drivers:
            for v1 in self.drivers:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.count is not None:
            result['Count'] = self.count

        result['Drivers'] = []
        if self.drivers is not None:
            for k1 in self.drivers:
                result['Drivers'].append(k1.to_map() if k1 else None)

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Count') is not None:
            self.count = m.get('Count')

        self.drivers = []
        if m.get('Drivers') is not None:
            for k1 in m.get('Drivers'):
                temp_model = main_models.DescribePeripheralDriversResponseBodyDrivers()
                self.drivers.append(temp_model.from_map(k1))

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        return self

class DescribePeripheralDriversResponseBodyDrivers(DaraModel):
    def __init__(
        self,
        ali_uid: int = None,
        brand: str = None,
        device_type: str = None,
        driver_id: str = None,
        driver_name: str = None,
        icon_path: str = None,
        oss_file_path: str = None,
    ):
        self.ali_uid = ali_uid
        self.brand = brand
        self.device_type = device_type
        self.driver_id = driver_id
        self.driver_name = driver_name
        self.icon_path = icon_path
        self.oss_file_path = oss_file_path

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.ali_uid is not None:
            result['AliUid'] = self.ali_uid

        if self.brand is not None:
            result['Brand'] = self.brand

        if self.device_type is not None:
            result['DeviceType'] = self.device_type

        if self.driver_id is not None:
            result['DriverId'] = self.driver_id

        if self.driver_name is not None:
            result['DriverName'] = self.driver_name

        if self.icon_path is not None:
            result['IconPath'] = self.icon_path

        if self.oss_file_path is not None:
            result['OssFilePath'] = self.oss_file_path

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('AliUid') is not None:
            self.ali_uid = m.get('AliUid')

        if m.get('Brand') is not None:
            self.brand = m.get('Brand')

        if m.get('DeviceType') is not None:
            self.device_type = m.get('DeviceType')

        if m.get('DriverId') is not None:
            self.driver_id = m.get('DriverId')

        if m.get('DriverName') is not None:
            self.driver_name = m.get('DriverName')

        if m.get('IconPath') is not None:
            self.icon_path = m.get('IconPath')

        if m.get('OssFilePath') is not None:
            self.oss_file_path = m.get('OssFilePath')

        return self

