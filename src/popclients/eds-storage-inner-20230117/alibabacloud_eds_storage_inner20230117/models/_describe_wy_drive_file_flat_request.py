# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 


class DescribeWyDriveFileFlatRequest(DaraModel):
    def __init__(
        self,
        max_results: int = None,
        next_token: str = None,
        parent_folder: str = None,
        product_type: str = None,
        user_ali_uid: int = None,
        wy_drive_owner_id: str = None,
    ):
        self.max_results = max_results
        self.next_token = next_token
        # This parameter is required.
        self.parent_folder = parent_folder
        # This parameter is required.
        self.product_type = product_type
        # This parameter is required.
        self.user_ali_uid = user_ali_uid
        # This parameter is required.
        self.wy_drive_owner_id = wy_drive_owner_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.max_results is not None:
            result['MaxResults'] = self.max_results

        if self.next_token is not None:
            result['NextToken'] = self.next_token

        if self.parent_folder is not None:
            result['ParentFolder'] = self.parent_folder

        if self.product_type is not None:
            result['ProductType'] = self.product_type

        if self.user_ali_uid is not None:
            result['UserAliUid'] = self.user_ali_uid

        if self.wy_drive_owner_id is not None:
            result['WyDriveOwnerId'] = self.wy_drive_owner_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('MaxResults') is not None:
            self.max_results = m.get('MaxResults')

        if m.get('NextToken') is not None:
            self.next_token = m.get('NextToken')

        if m.get('ParentFolder') is not None:
            self.parent_folder = m.get('ParentFolder')

        if m.get('ProductType') is not None:
            self.product_type = m.get('ProductType')

        if m.get('UserAliUid') is not None:
            self.user_ali_uid = m.get('UserAliUid')

        if m.get('WyDriveOwnerId') is not None:
            self.wy_drive_owner_id = m.get('WyDriveOwnerId')

        return self

