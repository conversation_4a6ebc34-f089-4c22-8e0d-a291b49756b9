# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_eds_storage_inner20230117 import models as main_models 
from typing import List


class DescribeGlobalCloudDrivesResponseBody(DaraModel):
    def __init__(
        self,
        cloud_drives: List[main_models.DescribeGlobalCloudDrivesResponseBodyCloudDrives] = None,
        count: int = None,
        request_id: str = None,
        success: bool = None,
    ):
        self.cloud_drives = cloud_drives
        self.count = count
        self.request_id = request_id
        self.success = success

    def validate(self):
        if self.cloud_drives:
            for v1 in self.cloud_drives:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        result['CloudDrives'] = []
        if self.cloud_drives is not None:
            for k1 in self.cloud_drives:
                result['CloudDrives'].append(k1.to_map() if k1 else None)

        if self.count is not None:
            result['Count'] = self.count

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.success is not None:
            result['Success'] = self.success

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        self.cloud_drives = []
        if m.get('CloudDrives') is not None:
            for k1 in m.get('CloudDrives'):
                temp_model = main_models.DescribeGlobalCloudDrivesResponseBodyCloudDrives()
                self.cloud_drives.append(temp_model.from_map(k1))

        if m.get('Count') is not None:
            self.count = m.get('Count')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('Success') is not None:
            self.success = m.get('Success')

        return self

class DescribeGlobalCloudDrivesResponseBodyCloudDrives(DaraModel):
    def __init__(
        self,
        directory_type: str = None,
        domain_id: str = None,
        drive_id: str = None,
        drive_name: str = None,
        region_id: str = None,
        user_id: str = None,
    ):
        self.directory_type = directory_type
        self.domain_id = domain_id
        self.drive_id = drive_id
        self.drive_name = drive_name
        self.region_id = region_id
        self.user_id = user_id

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.directory_type is not None:
            result['DirectoryType'] = self.directory_type

        if self.domain_id is not None:
            result['DomainId'] = self.domain_id

        if self.drive_id is not None:
            result['DriveId'] = self.drive_id

        if self.drive_name is not None:
            result['DriveName'] = self.drive_name

        if self.region_id is not None:
            result['RegionId'] = self.region_id

        if self.user_id is not None:
            result['UserId'] = self.user_id

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('DirectoryType') is not None:
            self.directory_type = m.get('DirectoryType')

        if m.get('DomainId') is not None:
            self.domain_id = m.get('DomainId')

        if m.get('DriveId') is not None:
            self.drive_id = m.get('DriveId')

        if m.get('DriveName') is not None:
            self.drive_name = m.get('DriveName')

        if m.get('RegionId') is not None:
            self.region_id = m.get('RegionId')

        if m.get('UserId') is not None:
            self.user_id = m.get('UserId')

        return self

