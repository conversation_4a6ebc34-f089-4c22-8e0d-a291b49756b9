# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from __future__ import annotations
from darabonba.model import DaraModel 
from alibabacloud_eds_storage_inner20230117 import models as main_models 
from typing import List


class DescribeWyDriveFileFlatResponseBody(DaraModel):
    def __init__(
        self,
        code: str = None,
        message: str = None,
        request_id: str = None,
        success: bool = None,
        wy_drive_files: List[main_models.DescribeWyDriveFileFlatResponseBodyWyDriveFiles] = None,
    ):
        self.code = code
        self.message = message
        self.request_id = request_id
        self.success = success
        self.wy_drive_files = wy_drive_files

    def validate(self):
        if self.wy_drive_files:
            for v1 in self.wy_drive_files:
                 if v1:
                    v1.validate()

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.code is not None:
            result['Code'] = self.code

        if self.message is not None:
            result['Message'] = self.message

        if self.request_id is not None:
            result['RequestId'] = self.request_id

        if self.success is not None:
            result['Success'] = self.success

        result['WyDriveFiles'] = []
        if self.wy_drive_files is not None:
            for k1 in self.wy_drive_files:
                result['WyDriveFiles'].append(k1.to_map() if k1 else None)

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Code') is not None:
            self.code = m.get('Code')

        if m.get('Message') is not None:
            self.message = m.get('Message')

        if m.get('RequestId') is not None:
            self.request_id = m.get('RequestId')

        if m.get('Success') is not None:
            self.success = m.get('Success')

        self.wy_drive_files = []
        if m.get('WyDriveFiles') is not None:
            for k1 in m.get('WyDriveFiles'):
                temp_model = main_models.DescribeWyDriveFileFlatResponseBodyWyDriveFiles()
                self.wy_drive_files.append(temp_model.from_map(k1))

        return self

class DescribeWyDriveFileFlatResponseBodyWyDriveFiles(DaraModel):
    def __init__(
        self,
        name: str = None,
        path: str = None,
        size: int = None,
    ):
        self.name = name
        self.path = path
        self.size = size

    def validate(self):
        pass

    def to_map(self):
        result = dict()
        _map = super().to_map()
        if _map is not None:
            result = _map
        if self.name is not None:
            result['Name'] = self.name

        if self.path is not None:
            result['Path'] = self.path

        if self.size is not None:
            result['Size'] = self.size

        return result

    def from_map(self, m: dict = None):
        m = m or dict()
        if m.get('Name') is not None:
            self.name = m.get('Name')

        if m.get('Path') is not None:
            self.path = m.get('Path')

        if m.get('Size') is not None:
            self.size = m.get('Size')

        return self

