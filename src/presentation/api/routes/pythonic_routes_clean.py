# """
# 文件和Agent相关API路由
# 从原来的pythonic_routes.py中分离出会话相关功能后的文件
# """
#
# from datetime import datetime
# from typing import Optional, Dict, Any, List
# import uuid
# from fastapi import APIRouter, HTTPException, Request, Query, UploadFile, File, Form, Depends
# from fastapi.responses import StreamingResponse, PlainTextResponse
# from loguru import logger
# import httpx
#
# from ....application.api_models import (
#     FileUploadResponse, FileStatusResponse, SessionFilesResponse, DownloadUrlsResponse,
#     PresignedUploadRequest, PresignedUploadResponse, ConfirmUploadRequest, ConfirmUploadResponse,
#     ResourceType, SessionResource
# )
# from ....popclients.waiy_infra_client import WaiyInfraClient, WaiyInfraClientError
# from ....domain.services.file_service import file_service
# from ....domain.services.auth_service import AuthContext, require_auth
# from ....presentation.api.dependencies.common_params import CommonParams, UniversalCommonParams
# from ....infrastructure.database.models.file_models import FileType
#
# router = APIRouter(prefix="/api", tags=["file", "agent"])
#
#
# @router.get("/agents/list")
# async def list_agents():
#     """
#     获取所有可用的Agent列表
#     使用WaiyInfraClient调用无影AI内部服务获取应用程序列表
#     """
#     try:
#         # 使用WaiyInfraClient获取应用列表
#         client = WaiyInfraClient()
#         response = await client.list_apps_async()
#
#         # 转换数据格式，统一字段命名
#         agents = []
#         if hasattr(response, 'body') and hasattr(response.body, 'applications'):
#             applications = response.body.applications
#         elif hasattr(response, 'applications'):
#             applications = response.applications
#         else:
#             # 如果响应格式不明确，尝试直接解析
#             applications = getattr(response, 'applications', [])
#
#         for app in applications:
#             # 根据实际的响应结构提取数据
#             # 使用getattr安全地获取属性，如果不存在则返回None
#             agent_id = getattr(app, 'id', None)
#             name = getattr(app, 'name', None)
#             version = getattr(app, 'version', None)
#             description = getattr(app, 'description', None)
#             tags = getattr(app, 'tags', [])
#             mcp_servers = getattr(app, 'mcp_servers', [])
#
#             # 如果tags或mcp_servers是None，设置为空列表
#             if tags is None:
#                 tags = []
#             if mcp_servers is None:
#                 mcp_servers = []
#
#             agents.append({
#                 "agentId": agent_id,
#                 "name": name,
#                 "version": version,
#                 "description": description,
#                 "tags": tags,
#                 "mcpServers": mcp_servers
#             })
#
#         logger.info(f"[API] 获取Agent列表成功: {len(agents)}个")
#
#         return {
#             "agents": agents,
#             "total": len(agents)
#         }
#
#     except WaiyInfraClientError as e:
#         logger.error(f"[API] 调用WaiyInfraClient失败: {e}")
#         raise HTTPException(status_code=502, detail="无影AI内部服务不可用")
#     except Exception as e:
#         logger.error(f"[API] 获取Agent列表异常: {e}")
#         raise HTTPException(status_code=500, detail="内部服务器错误")
#
#
# # ==================== 文件服务接口 ====================
#
# @router.post("/files/get-upload-url", response_model=PresignedUploadResponse)
# async def create_presigned_upload(
#     request: PresignedUploadRequest,
#     context: AuthContext = Depends(require_auth)
# ):
#     """
#     创建预签名上传链接（新接口）
#     前端通过此接口获取上传链接，然后直接上传到OSS
#     """
#     try:
#         logger.info(f"[API] 创建预签名上传: user={context.user_key}, file={request.file_info.file_name}")
#
#         # 验证文件信息
#         if not request.file_info.file_name:
#             raise HTTPException(status_code=400, detail="文件名不能为空")
#
#         if request.file_info.file_size <= 0:
#             raise HTTPException(status_code=400, detail="文件大小必须大于0")
#
#         # 验证文件大小（9M限制）
#         max_size = 9 * 1024 * 1024
#         if request.file_info.file_size > max_size:
#             raise HTTPException(status_code=400, detail=f"文件大小超过限制，最大允许{max_size // (1024*1024)}MB")
#
#         # 如果没有提供session_id，会在service中自动生成
#         session_id = request.session_id
#
#         # 创建预签名上传
#         file_obj, upload_url, expires_in = file_service.create_presigned_upload(
#             context=context,
#             file_name=request.file_info.file_name,
#             file_size=request.file_info.file_size,
#             file_type=request.file_info.file_type,
#             session_id=session_id,
#             upload_file_type=request.file_type
#         )
#
#         response = PresignedUploadResponse(
#             file_id=str(file_obj.id),
#             session_id=file_obj.session_id,
#             upload_url=upload_url,
#             expires_in=expires_in,
#             file_name=file_obj.title,
#             file_size=file_obj.file_size or request.file_info.file_size,
#             upload_method="PUT",
#             headers={
#                 "Content-Type": file_obj.content_type or "application/octet-stream"
#             }
#         )
#
#         logger.info(f"[API] 预签名上传创建成功: file_id={file_obj.id}, expires_in={expires_in}s")
#         return response
#
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"[API] 创建预签名上传异常: {e}")
#         raise HTTPException(status_code=500, detail="创建预签名上传失败")
#
#
# @router.post("/files/confirm-upload", response_model=ConfirmUploadResponse)
# async def confirm_presigned_upload(
#     request: ConfirmUploadRequest,
#     context: AuthContext = Depends(require_auth)
# ):
#     """
#     确认预签名上传完成
#     前端上传完成后调用此接口确认，触发后续处理
#     """
#     try:
#         logger.info(f"[API] 确认预签名上传: user={context.user_key}, file_id={request.file_id}")
#
#         # 验证文件ID
#         try:
#             file_id = int(request.file_id)
#         except ValueError:
#             raise HTTPException(status_code=400, detail="无效的文件ID")
#
#         # 确认上传
#         success = file_service.confirm_presigned_upload(
#             context=context,
#             file_id=file_id,
#             etag=request.etag
#         )
#
#         if not success:
#             raise HTTPException(status_code=400, detail="确认上传失败，请检查文件是否已正确上传")
#
#         logger.info(f"[API] 预签名上传确认成功: file_id={file_id}")
#
#         return ConfirmUploadResponse(
#             file_id=request.file_id,
#             status="confirmed",
#             message="上传确认成功，文件正在处理中"
#         )
#
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"[API] 确认预签名上传异常: {e}")
#         raise HTTPException(status_code=500, detail="确认上传失败")
#
#
# @router.get("/files/session/{session_id}", response_model=SessionFilesResponse)
# async def get_session_files(
#     session_id: str,
#     file_types: Optional[str] = Query(None, description="文件类型过滤，多个类型用逗号分隔，如: resultArtifact,processArtifact"),
#     limit: int = Query(100, description="返回的文件数量限制"),
#     offset: int = Query(0, description="分页偏移量"),
#     common_params: CommonParams = UniversalCommonParams
# ):
#     """
#     获取某个会话下的所有文件
#     支持按文件类型过滤
#     """
#     try:
#         logger.info(f"[API] 获取会话文件: session_id={session_id}, file_types={file_types}, login_token={common_params.login_token}, region_id={common_params.region_id}")
#
#         # 解析文件类型过滤
#         file_types_list = None
#         if file_types:
#             file_types_list = [ft.strip() for ft in file_types.split(',') if ft.strip()]
#             # 验证文件类型
#             valid_types = [FileType.RESULT_ARTIFACT.value, FileType.PROCESS_ARTIFACT.value, FileType.SESSION_FILE.value]
#             for ft in file_types_list:
#                 if ft not in valid_types:
#                     raise HTTPException(status_code=400, detail=f"无效的文件类型: {ft}")
#
#         # 获取文件列表
#         result = file_service.get_session_files(
#             session_id=session_id,
#             file_types=file_types_list,
#             limit=limit,
#             offset=offset
#         )
#
#         logger.info(f"[API] 获取会话文件成功: session_id={session_id}, count={result['total_count']}")
#         return SessionFilesResponse(**result)
#
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"[API] 获取会话文件异常: {e}")
#         raise HTTPException(status_code=500, detail="获取会话文件失败")
#
#
# @router.post("/files/download-urls", response_model=DownloadUrlsResponse)
# async def get_download_urls(
#     file_ids: List[int],
#     expires: int = Query(3600, description="下载链接过期时间（秒）"),
#     common_params: CommonParams = UniversalCommonParams
# ):
#     """
#     根据文件ID列表获取下载链接
#     批量获取多个文件的下载链接
#     """
#     try:
#         logger.info(f"[API] 获取下载链接: file_ids={file_ids}, expires={expires}, session_id={common_params.session_id}, region_id={common_params.region_id}")
#
#         if not file_ids:
#             raise HTTPException(status_code=400, detail="文件ID列表不能为空")
#
#         if len(file_ids) > 100:
#             raise HTTPException(status_code=400, detail="一次最多只能获取100个文件的下载链接")
#
#         if expires < 60 or expires > 86400:  # 1分钟到1天
#             raise HTTPException(status_code=400, detail="过期时间必须在60秒到86400秒之间")
#
#         # 获取下载链接
#         result = file_service.get_download_urls(file_ids, expires)
#
#         logger.info(f"[API] 获取下载链接成功: requested={len(file_ids)}, success={result['success_count']}")
#         return DownloadUrlsResponse(**result)
#
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"[API] 获取下载链接异常: {e}")
#         raise HTTPException(status_code=500, detail="获取下载链接失败")
#
#
# # 添加独立的路由器用于全局接口（不带/api前缀）
# global_router = APIRouter(tags=["health"])
#
# @global_router.get("/status.taobao", response_class=PlainTextResponse)
# async def status_taobao():
#     """
#     Taobao健康检查接口
#     匹配部署脚本中的健康检查要求
#     返回纯文本 "success"
#     """
#     try:
#         # 检查基本服务状态（移除日志输出以减少噪音）
#         return "success"
#     except Exception as e:
#         logger.error(f"[Health] Taobao健康检查失败: {e}")
#         raise HTTPException(status_code=500, detail="Service unhealthy")